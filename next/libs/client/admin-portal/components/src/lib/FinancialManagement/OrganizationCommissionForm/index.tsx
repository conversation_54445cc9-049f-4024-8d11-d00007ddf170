'use client';
import {
  Box,
  Button,
  ButtonGroup,
  Card,
  Flex,
  FormControl,
  FormLabel,
  Radio,
  RadioGroup,
  Spinner,
  Stack,
  Text,
} from '@chakra-ui/react';
import { useJavaApi } from '@next/shared/api';
import { DatePickerStyles } from '@next/shared/constants';
import { parseDateWithoutTimezone, useLocalStorage } from '@next/shared/hooks';
import {
  OrganizationCommissionState,
  OrganizationPlan,
  ResetTrigger,
} from '@next/shared/types';
import { FormControlDate, FormControlInput } from '@next/shared/ui';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { FieldValues, FormProvider, useForm, useWatch } from 'react-hook-form';

import { FormAlertDialogs } from '../BobCommissionForm/FormAlertDialogs';
import { withFormReadOnlyTheme } from '../withFormReadOnlyTheme/withFormReadOnlyTheme';
import AssignmentTypeSection from './helper-components/AssignmentTypeSection';
import { createUpdateDeleteOrg } from './helper-components/createUpdateDeleteOrgCommission';
import GenericSelect from './helper-components/GenericSelect';

// Constants and Styles
const containerStyles: any = {
  justifyContent: 'space-between',
  alignItems: 'baseline',
  paddingLeft: 2.5,
  display: 'flex',
  paddingBottom: 3,
};

const labelStyles: any = {
  fontSize: 'sm',
  minWidth: 36,
  textAlign: 'end',
  fontWeight: 'medium',
};

const dateStyles = DatePickerStyles({
  inputProps: { height: '38px', borderRadius: 4 },
});

const OrganizationCommissionFormUI = (initialData: any) => {
  // State Management
  const methods = useForm();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { methodApi, getApi } = useJavaApi();
  const brokerName = searchParams?.get('brokerName');
  const returnTo = searchParams?.get('returnTo');
  const urlCommConfigNo = searchParams?.get('commConfigNo');
  const edit = searchParams?.get('edit');
  const orgId = searchParams?.get('orgId');
  const orgName = searchParams?.get('orgName');
  const orgEditable = searchParams?.get('orgEditable');
  const urlCommBrokerNo = searchParams?.get('id');

  const editCommission = edit === 'true';
  const localCustomCommission = useRef(
    useLocalStorage().getLocalStorageItem('custom_commission_no')
  );
  const savedFormValuesString = sessionStorage.getItem(
    'organizationCommissionForm'
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const initialState = useMemo(
    () => ({
      showCancelWarning: false,
      showSaveEditDialog: false,
      showRemoveDialog: false,
      showDuplicateDialog: false,
      loading: true,
      commissionData: undefined,
      orgPlans: [],
      commConfigNo: Number(urlCommConfigNo),
      editMode: false,
      disabled: initialData?.orgEditable === 'false',
      assignmentType: 'standard',
      saveDuplicate: false,
      resetTrigger: {
        standard: false,
        custom: false,
        commission: false,
      },
      shouldResetForm: false,
    }),
    [urlCommConfigNo, initialData?.orgEditable]
  );

  const [state, setState] = useState<OrganizationCommissionState>(initialState);

  const updateState = useCallback(
    (
      updates:
        | Partial<OrganizationCommissionState>
        | ((
            prevState: OrganizationCommissionState
          ) => Partial<OrganizationCommissionState>)
    ) => {
      setState((prevState) => ({
        ...prevState,
        ...(typeof updates === 'function' ? updates(prevState) : updates),
      }));
    },
    [setState]
  );

  // References
  const formStateRef = useRef<FieldValues | null>(null);
  const cancelRef = useRef<HTMLButtonElement | null>(null);

  // Callbacks
  const fetchPlansForOrganization = useCallback(
    async (organizationNo: number, planNo?: number) => {
      try {
        // Fetch plans and transform them into the required format
        const plans = await getApi('organizationPlans', {
          organizationNo,
        }).then((data: OrganizationPlan[]) =>
          data.map(({ planNo, name }) => ({ value: planNo, label: name }))
        );

        // Extract plan details from initialData
        const { planNo: initialPlanNo, name: initialPlanName } =
          initialData?.brokerCommission?.plan || {};

        // Add the initial plan to the list if it doesn't exist
        if (
          initialPlanNo &&
          initialPlanName &&
          !plans.some(({ value }) => value === initialPlanNo)
        ) {
          plans.push({ value: initialPlanNo, label: initialPlanName });
        }

        // Determine the default plan
        const defaultPlan = plans.find(
          ({ value }) => value === Number(planNo || initialPlanNo)
        );
        if (defaultPlan) methods.setValue('plan', defaultPlan);

        // Update state with the final plans
        updateState({ orgPlans: plans, loading: false });
      } catch (error) {
        console.error('Error fetching plans:', error);
        updateState({ loading: false });
      }
    },
    [getApi, initialData?.brokerCommission?.plan, methods, updateState]
  );

  const updateCustomCommission = useCallback(() => {
    if (localCustomCommission.current) {
      updateState({
        commConfigNo: localCustomCommission.current.customCommConfig,
      });
    }
  }, [updateState]);

  const setFormValues = useCallback(async () => {
    const savedFormValues = savedFormValuesString
      ? JSON.parse(savedFormValuesString)
      : null;
    const { organizations, brokerCommission } = initialData;
    const orgIdNumber = savedFormValues?.organization?.value ?? orgId;
    const planNumber = savedFormValues?.plan?.value;

    const defaultOrg = organizations.find(
      (org: any) => org.value === Number(orgIdNumber)
    );

    if (defaultOrg) {
      methods.setValue('organization', defaultOrg);

      if (brokerCommission || savedFormValues) {
        const effectiveDate = savedFormValues?.dateEff
          ? parseDateWithoutTimezone(savedFormValues.dateEff)
          : brokerCommission?.dateEff
          ? parseDateWithoutTimezone(brokerCommission.dateEff)
          : undefined;

        const expirationDate = savedFormValues?.dateExp
          ? parseDateWithoutTimezone(savedFormValues.dateExp)
          : brokerCommission?.dateExp
          ? parseDateWithoutTimezone(brokerCommission.dateExp)
          : undefined;

        methods.setValue(
          'primaryBrokerName',
          brokerCommission?.primaryBrokerName
        );
        methods.setValue('dateEff', effectiveDate);
        methods.setValue('dateExp', expirationDate);
      }

      const assignmentType =
        brokerCommission?.sharedConfigIndicator === 0 ||
        localCustomCommission.current
          ? 'custom'
          : brokerCommission?.sharedConfigIndicator === 1
          ? 'standard'
          : 'no_commission';

      if (assignmentType === 'custom') {
        updateCustomCommission();
      }

      updateState({ assignmentType });

      await fetchPlansForOrganization(defaultOrg.value, planNumber);
    }
  }, [
    savedFormValuesString,
    initialData,
    orgId,
    methods,
    updateState,
    fetchPlansForOrganization,
    updateCustomCommission,
  ]);

  const resetFormValues = useCallback(async () => {
    methods.reset();
    localStorage.removeItem('custom_commission_no');
    sessionStorage.removeItem('organizationCommissionForm');
    sessionStorage.removeItem('formSubmitted');
    setState((prevState) => ({
      ...initialState,
      resetTrigger: {
        ...prevState.resetTrigger,
        commission: true,
      },
    }));
  }, [methods, initialState]);

  const handleNavigate = useCallback(() => {
    if (returnTo) {
      updateState({
        shouldResetForm: true,
      });
      router.push(String(returnTo));
    }
  }, [returnTo, router, updateState]);

  const updateCommConfigNumber = useCallback(
    (newCommConfigNo: number) => {
      if (newCommConfigNo !== state.commConfigNo) {
        updateState({ commConfigNo: newCommConfigNo });
      }
    },
    [state.commConfigNo, updateState]
  );

  useEffect(() => {
    if (state.shouldResetForm) {
      resetFormValues();
      updateState({ shouldResetForm: false });
    }
  }, [resetFormValues, state.shouldResetForm, updateState]);

  const handleDuplicate = useCallback(
    (shouldContinue: boolean) => {
      updateState({
        showDuplicateDialog: false,
        saveDuplicate: shouldContinue,
      });
    },
    [updateState]
  );

  const handleSaveEdit = useCallback(async () => {
    formStateRef.current = methods.getValues();
    const trigger = state.assignmentType === 'custom' ? 'standard' : 'custom';

    if (trigger === 'custom') {
      localCustomCommission.current = undefined;
      localStorage.removeItem('custom_commission_no');
    }

    updateState((prevState) => ({
      showSaveEditDialog: false,
      editMode: false,
      resetTrigger: {
        ...prevState.resetTrigger,
        [trigger]: true,
      },
    }));
  }, [methods, state.assignmentType, updateState]);

  const handleAssignmentTypeChange = (value: string) =>
    updateState({ assignmentType: value, commConfigNo: undefined });

  const onOrganizationChange = async (organization: any) => {
    methods.setValue('plan', null);
    methods.setValue('organization', organization);
    await fetchPlansForOrganization(organization.value);
  };

  const onPlanChange = async (plan: any) => methods.setValue('plan', plan);

  // Effects
  useEffect(() => {
    if (
      (editCommission && initialData.brokerCommission && state.loading) ||
      (savedFormValuesString && !editCommission) ||
      orgName
    )
      setFormValues();
    if (orgEditable)
      updateState({ disabled: editCommission && !state.editMode });
  }, [
    editCommission,
    initialData,
    state.loading,
    orgEditable,
    setFormValues,
    state.editMode,
    updateState,
    savedFormValuesString,
    orgName,
    state.disabled,
  ]);

  useEffect(() => {
    if (!editCommission && localCustomCommission.current) {
      updateCustomCommission();
    }
  }, [editCommission, updateCustomCommission]);

  const [dateEff, dateExp] = useWatch({
    control: methods.control,
    name: ['dateEff', 'dateExp'],
  });

  useEffect(() => {
    if (!dateEff) methods.setValue('dateExp', undefined);
  }, [dateEff, methods]);

  // Handlers
  const enterEditMode = () => {
    formStateRef.current = {
      formValues: methods.getValues(),
      componentState: state,
    };
    updateState({ editMode: true, disabled: false });
  };

  const exitEditMode = () => {
    if (formStateRef.current) {
      const currentAssignmentType = state.assignmentType;
      const initialAssignmentType =
        formStateRef.current.formValues.assignmentType;

      const trigger =
        currentAssignmentType === 'custom' ? 'standard' : 'custom';

      if (trigger !== initialAssignmentType) {
        updateState((prevState) => ({
          showSaveEditDialog: false,
          editMode: false,
          resetTrigger: {
            ...prevState.resetTrigger,
            [trigger]: true,
          },
        }));
      }
      methods.reset(formStateRef.current.formValues);
      setState(formStateRef.current.componentState);
    } else {
      updateState({ editMode: false, disabled: true });
    }
  };

  const handleCommissionOperation = useCallback(
    async (dataOrType: FieldValues | string) => {
      const { brokerCommission } = initialData || {};
      const { commBrokerAssignmentNo, versionNumber, commBrokerNo, planNo } =
        brokerCommission || {};

      try {
        if (dataOrType === 'DELETE') {
          await createUpdateDeleteOrg(
            'DELETE',
            { commBrokerAssignmentNo },
            methodApi,
            handleNavigate
          );
        } else {
          const data = dataOrType as FieldValues;
          const postData = {
            ...data,
            organizationNo: data.organization.value,
            versionNumber,
            commBrokerAssignmentNo,
            commConfigNo: state.commConfigNo,
            commBrokerNo: commBrokerNo || urlCommBrokerNo,
            planNo: planNo || methods.getValues('plan').value || '',
            primaryBrokerName: methods.getValues('primaryBrokerName') || '',
          };

          const opperation = editCommission ? 'PUT' : 'POST';
          const save =
            opperation === 'POST' && !state.saveDuplicate ? false : true;

          await createUpdateDeleteOrg(
            opperation,
            postData,
            methodApi,
            handleNavigate,
            (show, save) =>
              updateState({ showDuplicateDialog: show, saveDuplicate: save }),
            save
          );
        }
      } catch (error) {
        console.error(error);
      }
    },
    [
      initialData,
      methodApi,
      handleNavigate,
      state.commConfigNo,
      state.saveDuplicate,
      urlCommBrokerNo,
      methods,
      editCommission,
      updateState,
    ]
  );

  useEffect(() => {
    if (state.saveDuplicate) {
      handleCommissionOperation(methods.getValues());
    }
  }, [state.saveDuplicate, methods, handleCommissionOperation]);

  const handleResetTriggerChange = (updatedTrigger: ResetTrigger) => {
    updateState((prevState) => ({
      ...prevState,
      resetTrigger: updatedTrigger,
    }));
  };

  const handleSubmitForm = async () => {
    try {
      setIsSubmitting(true);
      const dateEff = methods.getValues('dateEff');
      const dateExp = methods.getValues('dateExp');

      if (dateEff && dateExp && dateEff.getTime() === dateExp.getTime()) {
        // Both dates are the same
        updateState({ showRemoveDialog: true });
      } else {
        // Dates are different
        await handleCommissionOperation(methods.getValues());
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormProvider {...methods}>
      <Card
        borderRadius={14}
        width="full"
        maxW="750px"
        variant="outline"
        height="min-content"
        p={12}
        pt={10}
      >
        <form onSubmit={methods.handleSubmit(handleCommissionOperation)}>
          {state.loading && editCommission ? (
            <Box
              sx={{
                minHeight: '400px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '100%',
              }}
            >
              <Spinner
                thickness="4px"
                speed="0.65s"
                emptyColor="gray.200"
                color="#03A262"
              />
            </Box>
          ) : (
            <Flex direction="column" rowGap={3}>
              <FormControl mb={4}>
                <Flex align="center">
                  <FormLabel {...labelStyles} mb="0" w="150px">
                    Broker
                  </FormLabel>
                  <Text>{brokerName}</Text>
                </Flex>
              </FormControl>

              <GenericSelect
                name="organization"
                label="Organization"
                options={initialData.organizations}
                isRequired
                disabled={editCommission || !!orgName}
                defaultValue={methods.getValues('organization')}
                onChange={onOrganizationChange}
              />

              <GenericSelect
                name="plan"
                label="Plan"
                options={state.orgPlans}
                isRequired
                disabled={editCommission || !methods.getValues('organization')}
                defaultValue={methods.getValues('plan')}
                onChange={onPlanChange}
              />

              <Flex>
                <FormControlDate
                  name="dateEff"
                  label="Effective Date"
                  control={methods.control}
                  isDisabled={editCommission && !state.editMode}
                  isRequired
                  showClearButton
                  clearAction={() => {
                    methods.setValue('dateEff', null);
                    methods.setValue('dateExp', null);
                  }}
                  dateProps={{
                    propsConfigs: {
                      ...dateStyles,
                      inputProps: { size: 'sm', borderRadius: 4 },
                    },
                    maxDate: dateExp,
                  }}
                  onChange={(date) => methods.setValue('dateEff', date)}
                  defaultValue={methods.getValues('dateEff')}
                  containerProps={containerStyles}
                  labelProps={labelStyles}
                />

                <FormControlDate
                  name="dateExp"
                  label="Expiration Date"
                  control={methods.control}
                  isDisabled={!dateEff}
                  showClearButton
                  clearAction={() => methods.setValue('dateExp', null)}
                  dateProps={{
                    propsConfigs: {
                      ...dateStyles,
                      inputProps: { size: 'sm', borderRadius: 4 },
                    },
                    minDate: dateEff,
                  }}
                  onChange={(date) => methods.setValue('dateExp', date)}
                  defaultValue={methods.getValues('dateExp')}
                  containerProps={containerStyles}
                  labelProps={labelStyles}
                />
              </Flex>

              <FormControlInput
                inputProps={{
                  size: 'sm',
                  borderRadius: 4,
                  placeholder: 'Enter broker name',
                  defaultValue:
                    initialData?.brokerCommission?.primaryBrokerName,
                }}
                isDisabled={editCommission && !state.editMode}
                labelProps={labelStyles}
                containerProps={containerStyles}
                name="primaryBrokerName"
                label="Broker Name"
                control={methods.control}
                onChange={(event) =>
                  methods.setValue('primaryBrokerName', event.target.value)
                }
              />

              <FormControl mb={4}>
                <Flex align="center">
                  <FormLabel {...labelStyles} mb="0" w="150px">
                    Assignment Type
                  </FormLabel>
                  <RadioGroup
                    isDisabled={state.disabled}
                    onChange={handleAssignmentTypeChange}
                    value={state.assignmentType}
                  >
                    <Stack direction="row">
                      <Radio value="standard">Standard</Radio>
                      <Radio value="custom">Custom</Radio>
                      <Radio value="no_commission">No Commission</Radio>
                    </Stack>
                  </RadioGroup>
                </Flex>
              </FormControl>

              <AssignmentTypeSection
                formState={state}
                initialData={initialData}
                updatedCommConfigNo={updateCommConfigNumber}
                onAddCommissionPlanClick={() => {
                  const params = new URLSearchParams();
                  params.set('custom', 'true');
                  params.set('from', window.location.pathname);
                  router.push(
                    `/financials/standard-commissions/create?${params}`
                  );
                }}
                isDisabled={editCommission && !state.editMode}
                showEditCustomCommissionButton={
                  state.editMode || !editCommission
                }
                containerStyles={containerStyles}
                labelStyles={labelStyles}
                onResetTriggerChange={handleResetTriggerChange}
              />

              <Flex
                alignItems="center"
                h="min-content"
                justifyContent="space-between"
                pt={10}
              >
                {editCommission && !state.editMode && orgEditable !== 'false' && (
                  <ButtonGroup>
                    <Button
                      size="sm"
                      bgColor="#1C75B9"
                      color="white"
                      onClick={() => updateState({ showRemoveDialog: true })}
                    >
                      Remove Commission Plan
                    </Button>
                    <Button
                      size="sm"
                      bgColor="#1C75B9"
                      color="white"
                      onClick={enterEditMode}
                    >
                      Edit Commission Plan
                    </Button>
                  </ButtonGroup>
                )}
                {state.commissionData &&
                  localCustomCommission &&
                  state.assignmentType === 'custom' && (
                    <Button
                      size="sm"
                      bgColor="#1C75B9"
                      color="white"
                      ml={2}
                      onClick={() => {
                        const params = new URLSearchParams();
                        params.set(
                          'id',
                          state.commissionData?.commConfigNo.toString() || ''
                        );
                        params.set('custom', 'true');
                        params.set('editable', 'true');
                        params.set('from', window.location.pathname);
                        router.push(
                          `/financials/standard-commissions/[id]/edit?${params}`
                        );
                      }}
                    >
                      View Commission Plan
                    </Button>
                  )}
                {state.editMode ? (
                  <ButtonGroup ml="auto" size="sm" spacing={6}>
                    <Button colorScheme="gray" onClick={exitEditMode}>
                      Cancel Edits
                    </Button>
                    <Button
                      type="button"
                      colorScheme="blue"
                      onClick={() => updateState({ showSaveEditDialog: true })}
                    >
                      Save Edits
                    </Button>
                  </ButtonGroup>
                ) : (
                  <ButtonGroup ml="auto" size="sm" spacing={6}>
                    <Button colorScheme="gray" onClick={handleNavigate}>
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      colorScheme="blue"
                      size="sm"
                      onClick={handleSubmitForm}
                      isDisabled={isSubmitting}
                      isLoading={isSubmitting}
                    >
                      Apply
                    </Button>
                  </ButtonGroup>
                )}
              </Flex>
            </Flex>
          )}
          <FormAlertDialogs
            cancelRef={cancelRef}
            showRemoveDialog={state.showRemoveDialog}
            showCancelDialog={state.showCancelWarning}
            showSaveEditDialog={state.showSaveEditDialog}
            showDuplicateDialog={state.showDuplicateDialog}
            handleOnCancel={() => updateState({ showCancelWarning: false })}
            handleOnRemove={() => handleCommissionOperation('DELETE')}
            handleCancelRemove={() => updateState({ showRemoveDialog: false })}
            handleOnContinue={() => handleNavigate()}
            handleOnCancelEdit={() =>
              updateState({ showSaveEditDialog: false })
            }
            handleSaveEdit={handleSaveEdit}
            handleDuplicateContinue={() => handleDuplicate(true)}
            handleDuplicateCancel={() => handleDuplicate(false)}
          />
        </form>
      </Card>
    </FormProvider>
  );
};

export const OrganizationCommissionForm = withFormReadOnlyTheme(
  OrganizationCommissionFormUI
);
