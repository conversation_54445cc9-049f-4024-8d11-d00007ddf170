'use client';
import { <PERSON>, Flex, <PERSON>, Spinner, Text } from '@chakra-ui/react';
import { useApi } from '@next/shared/api';
import { useCustomToast, useQueryParams } from '@next/shared/hooks';
import { format } from 'date-fns';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { BiCalendarEdit, BiEdit, BiSliderAlt } from 'react-icons/bi';
import { FaRegCalendarPlus, FaRegEye } from 'react-icons/fa';
import { IoMdStarOutline } from 'react-icons/io';
import { LuRefreshCcwDot } from 'react-icons/lu';
import { RiDeleteBinLine } from 'react-icons/ri';

import { ActionMenu, GeneratedReportsFilters } from '../SharedComponents';
import { ExpandableTableV2 } from '../SharedComponents/ExpandableTableV2';
import ReportModal from '../SharedComponents/ReportModal';
import { ScheduleDrawerContent } from '../SharedComponents/ScheduleDrawerContent';
import { FavouriteDetails } from './SubComponent/FavouriteDetails';

interface ReportingSavedProps {
  a?: any;
}

interface dataInterface {
  category: string;
  createdBy: string;
  createdOn: string;
  'definedFilters.filter.startYear': number;
  'definedFilters.filter.endMonth': number;
  'definedFilters.filter.endYear': number;
  'definedFilters.filter.label': string;
  'definedFilters.filter.startMonth': number;
  'definedFilters.groups': any; // Adjust type if you know the structure
  'definedFilters.organization_name': string;
  'definedFilters.organization_number': number;
  deleted: boolean;
  description: string;
  downloadOnly: boolean;
  permissionSlug: string;
  reportId: number;
  reportName: string;
  saveReportId: number;
  scheduleDate: string;
  updatedOn: string;
}

export const ReportingSaved: React.FC<ReportingSavedProps> = ({ a }) => {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();

  const catIds = {
    Claims: 1000,
    'Drug Cost Insights': 1001,
    'Stop Loss': 1003,
  };
  const offset = 0;
  const limit = 50;

  const [expandedIndex, setExpandedIndex] = useState<number>(-1);

  const { getFavourites, getApi, methodApi } = useApi(
    ['getFavourites'],
    { offset, limit },
    {
      onSuccess(res) {
        if (res?.data?.length === 0) {
          setSpinner(false);
        }
      },
    }
  );

  const saveReportId = searchParams?.get('saveReportId');

  const flattenObject = (obj: any, parentKey = '', result?: any) => {
    for (const key in obj) {
      // eslint-disable-next-line no-prototype-builtins
      if (obj.hasOwnProperty(key)) {
        const newKey = parentKey ? `${parentKey}.${key}` : key;
        if (
          typeof obj[key] === 'object' &&
          obj[key] !== null &&
          !Array.isArray(obj[key])
        ) {
          flattenObject(obj[key], newKey, result);
        } else {
          result[newKey] = obj[key];
        }
      }
    }
    return result;
  };

  const methods = useForm();
  const showToast = useCustomToast();
  const btnRef = React.useRef<HTMLButtonElement>(null);

  const {
    title: tempTitle,
    desc: tempDesc,
    modal,
    id,
  } = useQueryParams(['title', 'desc', 'modal', 'id', 'baseID']);
  const title = decodeURIComponent(tempTitle);
  const desc = decodeURIComponent(tempDesc);

  const [reportsList, setReportsList] = useState([]);
  const [scheduleData, setScheduleData] = useState({});

  useEffect(() => {
    const foundIndex = reportsList.findIndex((row: any) => {
      return row.saveReportId === Number(saveReportId);
    });

    if (foundIndex !== -1) {
      const val = 3 + foundIndex * 4;
      setExpandedIndex(val);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reportsList]);

  const [spinner, setSpinner] = useState(true);
  const [isEdit, setIsEdit] = useState(false);
  const [modalState, setmodalState] = useState({
    modal: false,
    title: '',
    desc: '',
  });
  const [selectedData, setSelectedData] = useState<any>(null);

  const reRouting = (viewtype: string, data: dataInterface) =>
    `/reports/singleFavourite?saveReportId=${
      data?.saveReportId
    }&viewtype=${viewtype}&organizationId=${
      data['definedFilters.organization_number']
    }&orgName=${encodeURIComponent(
      data['definedFilters.organization_name']
    )}&reportId=${data?.reportId}&title=${encodeURIComponent(
      data?.reportName
    )}&desc=${encodeURIComponent(data?.description)}&reportSlug=${
      data.permissionSlug
    }`;

  useEffect(() => {
    if (modal === 'true') {
      setmodalState((prevState) => ({
        ...prevState,
        title: decodeURIComponent(title),
        desc: decodeURIComponent(desc),
      }));
    } else {
      setmodalState((prevState) => ({
        ...prevState,
        title: decodeURIComponent(title || ''),
        desc: decodeURIComponent(desc || ''),
      }));
    }
  }, [desc, modal, title]);

  const pageParam = useMemo(() => searchParams?.get('page'), [searchParams]);
  const sizeParam = useMemo(() => searchParams?.get('size'), [searchParams]);

  useEffect(() => {
    const watch = methods.watch;
    const { orgID, reportName, category } = watch();
    if (params?.type === 'favorites') {
      getApi(
        'getFavourites',
        {
          offset,
          limit,
          orgNo: orgID,
          categoryId: catIds[category as keyof typeof catIds],
          keyword: reportName,
        },
        {
          onSuccess: (data) => {
            const reportingList =
              data?.data.map((item: any) => flattenObject(item, '', {})) || [];
            setReportsList(reportingList);
          },
        }
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getApi, pageParam, sizeParam, offset, limit]);

  function getURL() {
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);

    const pageNo = params.get('page');
    const pageSize = params.get('size');

    return { pageNo, pageSize };
  }

  const handleEditFavourites = () => {
    if (
      selectedData &&
      modalState.title?.length <= 40 &&
      modalState.desc?.length <= 150
    ) {
      methodApi('postFavourites', {
        method: 'POST',
        body: {
          title: decodeURIComponent(modalState.title) || '',
          description: decodeURIComponent(modalState.desc) || '',
          reportId: selectedData?.reportId,
          configuration: {
            organization_number:
              +selectedData?.['definedFilters.organization_number'],
            organization_name:
              selectedData?.['definedFilters.organization_name'],
            filter: {
              adjudicationRange: {
                startMonth:
                  selectedData?.[
                    'definedFilters.filter.adjudicationRange.startMonth'
                  ],
                startYear:
                  selectedData?.[
                    'definedFilters.filter.adjudicationRange.startYear'
                  ],
                endMonth:
                  selectedData?.[
                    'definedFilters.filter.adjudicationRange.endMonth'
                  ],
                endYear:
                  selectedData?.[
                    'definedFilters.filter.adjudicationRange.endYear'
                  ],
              },
              label: selectedData?.['definedFilters.filter.label'],
              startMonth: selectedData?.['definedFilters.filter.startMonth'],
              startYear: selectedData?.['definedFilters.filter.startYear'],
              endMonth: selectedData?.['definedFilters.filter.endMonth'],
              endYear: selectedData?.['definedFilters.filter.endYear'],
            },
            groups: selectedData?.['definedFilters.groups'],
          },
        },
        onSuccess(res) {
          showToast({
            title: 'Success',
            description: 'Your favorite has been saved.',
            status: 'success',
            position: 'top',
          });
          setSelectedData(null);
          setmodalState({ ...modalState, modal: false });
          closeFavourite();
        },
      });
      return;
    }
    const params = new URLSearchParams();
    params.set('id', id);
    params.set('modal', 'true');
    params.set('title', encodeURIComponent(String(modalState.title)));
    params.set('desc', encodeURIComponent(String(modalState.desc)));

    methodApi('postFavourites', {
      method: 'PUT',
      body: {
        title: decodeURIComponent(modalState.title),
        description: decodeURIComponent(modalState.desc),
        savereportId: Number(id),
      },
      onSuccess(res) {
        showToast({
          title: 'Report favourite updated',
          status: 'success',
          position: 'top',
        });
        closeFavourite();
      },
    });
  };

  const ApplyFilter = () => {
    setSpinner(true);
    const watch = methods.watch;
    const { orgID, reportName, runDate, category } = watch();
    getApi(
      'getFavourites',
      {
        offset,
        limit,
        orgNo: orgID?.value || '',
        categoryId: catIds[category?.value as keyof typeof catIds] || '',
        keyword: reportName || '',
      },
      {
        onSuccess: (data) => {
          let reportingList = getFavourites?.data;
          //   const secReportingList = getFavourites?.data;
          if (reportName && reportName !== '') {
            // secReportingList = getFavourites?.data.filter(
            //   (item: any) => {
            //     const key = reportName.toLowerCase();
            //     if (
            //       item.category.toLowerCase().includes(key) ||
            //       item.definedFilters.organization_name
            //         .toLowerCase()
            //         .includes(key) ||
            //       item.definedFilters.filter.label
            //         .toLowerCase()
            //         .includes(key) ||
            //       item.description.toLowerCase().includes(key) ||
            //       item.reportName.toLowerCase().includes(key)
            //     )
            //       return true;
            //     return false;
            //   }
            //   // item.reportName.toLowerCase().includes(reportName.toLowerCase())
            // );
          }

          if (orgID) {
            // secReportingList = (secReportingList || []).filter((item: any) => {
            //   const ID = orgID.value;
            //   return item.definedFilters.organization_number === +ID;
            // });
          }
          if (category && category !== '') {
            // secReportingList = secReportingList.filter((item: any) => {
            //   return item?.category === category?.value;
            // });
          }
          if (runDate && runDate !== '') {
            // secReportingList = secReportingList.filter((item: any) => {
            //   const created = new Date(item.createdOn);
            //   const runDateSplit = runDate.split('-');
            //   /// There is some issue in the native JS date object with the date formats. Reformatted to fix.
            //   const run = new Date(
            //     `${runDateSplit[1]}-${runDateSplit[2]}-${runDateSplit[0]}`
            //   );
            //   return created.valueOf() === run.valueOf();
            // });
          }
          reportingList =
            data?.data.map((item: any) => flattenObject(item, '', {})) || [];
          setReportsList(reportingList);
          setSpinner(false);
        },
      }
    );
  };
  useEffect(() => {
    // if (getFavourites?.data?.length > 0) {
    ApplyFilter();
    // }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const deleteReport = (id: number) => {
    setSpinner(true);
    methodApi('deleteReport', {
      restParams: {
        id: id,
      },
      method: 'DELETE',
    }).then((response) => {
      showToast({
        status: 'success',
        title: 'Report deleted',
        description: 'Report Deleted Successfully',
        position: 'top',
        duration: 2000,
      });
      const watch = methods.watch;
      const { orgID, reportName, category } = watch();
      getApi(
        'getFavourites',
        {
          offset,
          limit,
          orgNo: orgID,
          categoryId: catIds[category as keyof typeof catIds],
          keyword: reportName,
        },
        {
          onSuccess: (data) => {
            const reportingList =
              data?.data.map((item: any) => flattenObject(item, '', {})) || [];
            setReportsList(reportingList);
          },
        }
      );
    });
    setSpinner(false);
  };

  const downloadSheetReport = (data: dataInterface) => {
    setSpinner(true);

    methodApi('generateReport', {
      method: 'POST',
      restParams: { reportId: data.reportId },
      body: {
        payload: {
          organization_number: data['definedFilters.organization_number'],
          organization_name: data['definedFilters.organization_name'],
          filter: {
            label: data['definedFilters.filter.label'],
            startMonth: data['definedFilters.filter.startMonth'],
            startYear: data['definedFilters.filter.startYear'],
            endMonth: data['definedFilters.filter.endMonth'],
            endYear: data['definedFilters.filter.endYear'],
          },
          groups: data['definedFilters.groups'],
        },

        title: data?.reportName,
        description: data?.description,
      },
      onSuccess: () => {
        showToast({
          status: 'success',
          title: 'Report Generated',
          description: 'Please wait while your request is processed.',
          position: 'top',
        });
        const route = window.location.href.split('/');
        route.pop();
        window.location.href =
          route.join('/') +
          `${route.indexOf('reports') > -1 ? '' : '/reports'}/downloads`;
      },
      onError: () => {
        setSpinner(false);
      },
    });
  };

  //   Edit Favorite Process

  const editFavorite = (data: dataInterface, isAdd?: boolean) => {
    if (isAdd) {
      setmodalState({
        ...modalState,
        modal: true,
        title: decodeURIComponent(data?.reportName),
        desc: decodeURIComponent(data?.description),
      });
      setSelectedData(data);
      //   onOpen();
      return;
    }
    const { pageNo, pageSize } = getURL();
    const params = new URLSearchParams();
    params.set('id', data.saveReportId.toString());
    params.set('baseID', data?.reportId.toString());
    params.set('modal', 'true');
    params.set('title', encodeURIComponent(String(data?.reportName)));
    params.set('desc', encodeURIComponent(String(data?.description)));
    params.set('page', pageNo?.toString() || '');
    params.set('size', pageSize?.toString() || '');
    router.push(`/reports/favorites?${params}`);
  };

  const closeFavourite = () => {
    setmodalState({ ...modalState, modal: false });

    const watch = methods.watch;
    const { orgID, reportName, category } = watch();
    getApi(
      'getFavourites',
      {
        offset,
        limit,
        orgNo: orgID,
        categoryId: catIds[category as keyof typeof catIds],
        keyword: reportName,
      },
      {
        onSuccess: (data) => {
          const reportingList =
            data?.data.map((item: any) => flattenObject(item, '', {})) || [];
          setReportsList(reportingList);
        },
      }
    );
  };

  const handleScheduleDrawerState = (data: dataInterface, type: string) => {
    const { pageNo, pageSize } = getURL();
    const params = new URLSearchParams();
    params.set('id', data.saveReportId.toString());
    params.set('baseID', data?.reportId.toString());
    params.set('drawer', 'schedule');
    params.set('drawertype', type);
    params.set(
      'organizationId',
      data['definedFilters.organization_number'].toString()
    );
    params.set('reportSlug', data.permissionSlug);
    params.set('page', pageNo?.toString() || '');
    params.set('size', pageSize?.toString() || '');

    router.push(`/reports/favorites?${params}`);
  };
  const closeScheduleDrawer = () => {
    const { pageNo, pageSize } = getURL();
    const watch = methods.watch;
    const { orgID, reportName, category } = watch();
    getApi(
      'getFavourites',
      {
        offset,
        limit,
        orgNo: orgID,
        categoryId: catIds[category as keyof typeof catIds],
        keyword: reportName,
      },
      {
        onSuccess: (data) => {
          const reportingList =
            data?.data.map((item: any) => flattenObject(item, '', {})) || [];
          setReportsList(reportingList);
        },
      }
    );

    router.push(`/reports/favorites?page=${pageNo}&size=${pageSize}`);

    // getApi('getFavourites');
  };
  // Action Menu Constants --------------------------------

  const actionMenu = useMemo(
    () => (data: dataInterface) => {
      return [
        {
          clickHandler: () => {
            downloadSheetReport(data);
          },
          label: 'Generate Report',
          icon: <LuRefreshCcwDot fontSize={'18px'} />,
        },
        {
          clickHandler: () => {
            router.push(reRouting('preview', data));
          },
          label: 'Preview Data',
          icon: <FaRegEye fontSize={'18px'} />,
        },

        {
          clickHandler: () => {
            editFavorite(data, true);
            setIsEdit(false);
          },
          label: 'Save As Favorite',
          icon: <IoMdStarOutline fontSize={'18px'} />,
        },
        {
          type: 'line',
        },
        {
          clickHandler: () => {
            editFavorite(data);
            setmodalState({
              modal: true,
              title: decodeURIComponent(data?.reportName),
              desc: decodeURIComponent(data?.description),
            });
            setIsEdit(true);
          },
          label: 'Edit Favorite',
          icon: <BiEdit fontSize={'18px'} />,
        },
        !data.scheduleDate
          ? {
              clickHandler: () => {
                handleScheduleDrawerState(data, 'add');
                setScheduleData(data);
              },
              label: 'Add Schedule',
              icon: <FaRegCalendarPlus fontSize={'18px'} />,
            }
          : { type: null },
        data.scheduleDate && data.scheduleDate !== ''
          ? {
              clickHandler: () => {
                handleScheduleDrawerState(data, 'edit');
                setScheduleData(data);
              },
              label: 'Edit Schedule',
              icon: <BiCalendarEdit fontSize={'18px'} />,
            }
          : { type: null },
        {
          clickHandler: () => {
            router.push(reRouting('edit', data));
          },
          label: 'Modify Report Configuration',
          icon: <BiSliderAlt fontSize={'18px'} />,
        },
        {
          type: 'line',
        },
        {
          clickHandler: () => {
            deleteReport(data.saveReportId);
          },
          label: 'Delete',
          icon: <RiDeleteBinLine fontSize={'18px'} />,
        },
      ];
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const emptyMessageState = () => {
    return (
      <Text fontFamily={'300'} fontSize="16px">
        Save a report as favorite from{' '}
        <Link href="/reports/catalog" color="#2A69AC" textDecoration="none">
          Reports Catalog
        </Link>{' '}
        /{' '}
        <Link href="/reports/downloads" color="#2A69AC" textDecoration="none">
          My Downloads
        </Link>{' '}
        or try adjusting the filters.
      </Text>
    );
  };

  const columns = useMemo(() => {
    return [
      {
        Header: 'Report Name',
        accessor: 'reportName',
        enableSorting: true,
        customStyle: {
          padding: '20px',
          minWidth: '20%',
          whiteSpace: 'break-spaces',
          fontSize: '14px',
        },
        Cell: (info: dataInterface) => (
          <Flex flexDirection={'column'}>
            <Text fontSize={'14px'}>{info.reportName}</Text>
            <Text fontSize={11}>
              {info['definedFilters.organization_name']}
            </Text>
          </Flex>
        ),
      },
      {
        Header: 'Category',
        accessor: 'category',
        enableSorting: true,
        customStyle: {
          padding: '20px',
          minWidth: '20%',
          whiteSpace: 'break-spaces',
          fontSize: '14px',
        },
        Cell: (info: any) => (
          <Text style={{ textTransform: 'capitalize', fontSize: '14px' }}>
            {info?.['category']}
          </Text>
        ),
      },
      {
        Header: 'Scheduled Frequency',
        accessor: 'runDate',
        enableSorting: true,
        customStyle: {
          padding: '20px',
          minWidth: '20%',
          whiteSpace: 'break-spaces',
          fontSize: '14px',
        },
        Cell: (info: any) => {
          // to return new key
          if (info?.scheduleFrequency)
            return <Text fontSize={'14px'}>{info?.scheduleFrequency}</Text>;
          return <Text fontSize={'14px'}>None</Text>;
        },
      },
      {
        Header: 'Scheduled Run Date',
        accessor: 'createdOn',
        enableSorting: true,
        type: 'date',
        customStyle: {
          padding: '20px',
          minWidth: '20%',
          whiteSpace: 'break-spaces',
          fontSize: '14px',
        },
        Cell: (info: any) => {
          if (!info.scheduleDate) return <Text fontSize={'14px'}>None</Text>;
          return (
            <Text fontSize={'14px'}>
              {format(new Date(info.scheduleDate), 'MMMM dd, yyyy')}
            </Text>
          );
        },
      },
      {
        type: 'action',
        jsx: (info: any) => {
          return <ActionMenu data={actionMenu(info)} loading={false} />;
        },
        Header: 'Action',
        accessor: 'action',
        customStyle: {
          fontSize: '14px',
        },
      },
    ];
  }, [actionMenu]);
  const details = (row: dataInterface) => <FavouriteDetails row={row} />;

  return (
    <>
      <ScheduleDrawerContent
        onCloseHandler={closeScheduleDrawer}
        addDrawer={scheduleData}
      />
      <ReportModal
        btnRef={btnRef}
        isOpen={modalState.modal}
        onClose={() => setmodalState({ ...modalState, modal: false })}
        title={decodeURIComponent(modalState.title)}
        header={isEdit ? 'Edit Favorite' : 'Save As Favorite'}
        description={decodeURIComponent(modalState.desc)}
        handleFavourite={() => handleEditFavourites()}
        setmodalState={setmodalState}
      />
      <FormProvider {...methods}>
        <Text
          alignSelf={'flex-start'}
          color="#1A202C"
          fontFamily={'roboto'}
          fontSize="16px"
          fontWeight="400"
          letterSpacing="0.2px"
          lineHeight="24px"
          mb={3}
          mt={0}
        >
          This is a list of all reports you have saved as a favorite for
          recurring or ad hoc reports.
        </Text>
        <Text
          alignSelf={'flex-start'}
          color="#1A202C"
          fontFamily={'roboto'}
          fontSize="16px"
          fontWeight="400"
          letterSpacing="0.2px"
          lineHeight="24px"
          mb={6}
          mt={0}
        >
          Perform actions such as previewing data, generating an ad hoc report,
          scheduling recurring reports to be distributed among users or expand
          report details.
        </Text>
        <form>
          <GeneratedReportsFilters
            noRunDate
            hasCategories
            searchKey={'Keyword'}
            searchPlaceholder={'Report name, description'}
            reportsList={getFavourites?.data || []}
            clickHandler={ApplyFilter}
            resetHandler={ApplyFilter}
          />
        </form>
      </FormProvider>
      {spinner ? (
        <Center paddingY="48">
          <Spinner
            thickness="4px"
            speed="0.65s"
            emptyColor="gray.200"
            color="#03A262"
            size="xl"
          />
        </Center>
      ) : (
        <ExpandableTableV2
          data={reportsList}
          columns={columns as any}
          detailContainer={details}
          containerStyles={{
            backgroundColor: 'white',
            width: '100%',
          }}
          paginationSize={50}
          bodyStyles={{ fontSize: '14px' }}
          emptyStateTitle="No Favorites found"
          emptyStateMessage={emptyMessageState()}
          setExpandedIndex={setExpandedIndex}
          expandedIndex={expandedIndex}
        />
      )}
    </>
  );
};
