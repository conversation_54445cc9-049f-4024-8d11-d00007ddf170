import { Badge } from '@chakra-ui/react';
import React from 'react';

interface UserStatusTagProps {
  status: string;
}

export const UserStatusTag: React.FC<UserStatusTagProps> = ({ status }) => {
  const getStatus = () => {
    switch (status) {
      case 'Active':
        return (
          <Badge
            fontSize="12px"
            fontWeight="700"
            bg="rgba(144, 196, 0, 0.20)"
            color="#003D1B"
            borderRadius="2px"
          >
            ACTIVE
          </Badge>
        );
      case 'Blocked':
        return (
          <Badge
            fontSize="12px"
            fontWeight="700"
            variant="outline"
            color="#942722"
            border="1px solid #942722"
            borderRadius="2px"
          >
            BLOCKED
          </Badge>
        );
      default:
        return (
          <Badge
            fontSize="12px"
            fontWeight="700"
            lineHeight="16px"
            variant="subtle"
            backgroundColor="black"
            colorScheme="whiteAlpha"
            borderRadius="2px"
          >
            Unknown
          </Badge>
        );
    }
  };

  return <div>{getStatus()}</div>;
};
