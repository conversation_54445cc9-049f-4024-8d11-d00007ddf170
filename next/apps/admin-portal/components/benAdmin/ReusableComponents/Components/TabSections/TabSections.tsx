import {
  Box,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Tag,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import {
  LegalEntityAssignments,
  OrganizationDetails,
  PlanDesign,
  useSaveChangeRequestHandler,
} from 'apps/admin-portal/components/benAdmin';
import React, { useContext } from 'react';

import { ValidationContext } from '../../../organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { TabSectionProps } from '../../Models/types';
import { useFormCompletionTracking } from '../Form/components/hooks/Completion/useFormCompletion';
import { SimpleDropdown } from '../Form/components/SimpleDropdown';
import PublishConfirmModal from './PublishModal';

// Fields to check for non-null values in ancillaries
const DATA_FIELDS = [
  'effective_date',
  'expiration_date',
  'eligibility_export_ind',
  'pharmacy_claim_export_ind',
  'notes',
];

// Helper: Filter out plan design ancillaries with empty/null required fields
function cleanPlanDesignAncillaries(planDesigns: PlanDesign[]): PlanDesign[] {
  return planDesigns.map((planDesign: PlanDesign) => {
    if (!Array.isArray(planDesign.plan_design_ancillaries)) return planDesign;

    const filteredAncillaries = planDesign.plan_design_ancillaries.filter(
      (anc: any) => {
        return DATA_FIELDS.some(
          (key) =>
            anc[key] !== null && anc[key] !== undefined && anc[key] !== ''
        );
      }
    );

    return {
      ...planDesign,
      plan_design_ancillaries: filteredAncillaries,
    };
  });
}

// Helper: Filter out legal entity assignments with null phone numbers when key fields are populated
function cleanLegalEntityAssignments(
  assignments: LegalEntityAssignments[]
): LegalEntityAssignments[] {
  const hasValue = (value: any) =>
    value !== null && value !== undefined && value !== '';

  return assignments.filter((assignment: LegalEntityAssignments) => {
    const phones = assignment?.source_legal_entity?.phones;
    const role = assignment?.role;

    // If key fields all have values, phone number must also have a value
    const keyFieldsPopulated =
      hasValue(phones?.is_primary) &&
      hasValue(role?.allow_multiple_ind) &&
      hasValue(role?.name);

    return !keyFieldsPopulated || hasValue(phones?.phone_number);
  });
}

// Helper: Clean form data before publishing
function cleanForPublish(formData: OrganizationDetails): OrganizationDetails {
  if (!formData?.plan) return formData;

  let cleanedPlan = { ...formData.plan };

  // Clean plan design ancillaries
  if (formData.plan.plan_designs) {
    cleanedPlan = {
      ...cleanedPlan,
      plan_designs: cleanPlanDesignAncillaries(formData.plan.plan_designs),
    };
  }

  // Clean legal entity assignments
  if (Array.isArray(formData.plan.legal_entity_assignment)) {
    cleanedPlan = {
      ...cleanedPlan,
      legal_entity_assignment: cleanLegalEntityAssignments(
        formData.plan.legal_entity_assignment
      ),
    };
  }

  return {
    ...formData,
    plan: cleanedPlan,
  };
}

const TabSection: React.FC<TabSectionProps> = ({
  tabs,
  isGuided = false,
  formMethods,
}) => {
  const { overallCompletion } = useFormCompletionTracking();

  // This component can also exist outside of the ValidationProvider (on the OrganizationView screen)
  const validationContext = useContext(ValidationContext);

  const {
    isOpen: isPublishModalOpen,
    onOpen: onPublishModalOpen,
    onClose: onPublishModalClose,
  } = useDisclosure();
  const toast = useToast();
  const publishHandler = useSaveChangeRequestHandler(
    formMethods || ({} as any),
    true
  );
  const saveHandler = useSaveChangeRequestHandler(
    formMethods || ({} as any),
    false,
    true
  );
  const isDirty = formMethods?.formState.isDirty || false;

  const renderHeader = () => (
    <Box
      display="flex"
      flexDirection="row"
      justifyContent="space-between"
      alignItems="center"
      mb={4}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Text fontSize="20px" fontWeight="bold" mr={4}>
          Plan Design
        </Text>
        <Box display="flex">
          {overallCompletion === 100 ? (
            <Tag colorScheme="green" size="sm">
              Published
            </Tag>
          ) : (
            <Tag colorScheme="orange" size="sm">
              In Progress
            </Tag>
          )}
        </Box>
      </Box>
      <SimpleDropdown
        placeholder="Actions"
        optionsMap={{
          'Publish Plan Design': 'Publish Plan Design',
        }}
        onChange={(selectedKey: string) => {
          if (selectedKey === 'Publish Plan Design') {
            onPublishModalOpen();
          }
        }}
        value=""
        width="200px"
      />
    </Box>
  );

  return (
    <Tabs variant="enclosed" mt={12}>
      <TabList display="inline-flex" borderBottom="2px solid #008750">
        {tabs.map((tab, index) => (
          <Box
            key={tab.label || index}
            borderTopLeftRadius="2xl"
            borderTopRightRadius="2xl"
            bg="white"
            ml={index > 0 ? 2 : 0}
          >
            <Tab
              _selected={{ color: 'white', bg: '#008750' }}
              textColor="#008750"
              borderTopLeftRadius="2xl"
              borderTopRightRadius="2xl"
            >
              <Text fontSize="md" fontWeight="normal">
                {tab.label}
              </Text>
            </Tab>
          </Box>
        ))}
      </TabList>

      <TabPanels mt={0.5}>
        {tabs.map((tab, index) => (
          <Box
            bg="white"
            key={tab.label || index}
            borderBottomLeftRadius="md"
            borderBottomRightRadius="md"
            borderTopRightRadius="md"
          >
            <TabPanel p={4}>
              {' '}
              {index === 0 && isGuided && renderHeader()}
              {tab?.component}
            </TabPanel>
          </Box>
        ))}
      </TabPanels>

      <PublishConfirmModal
        isOpen={isPublishModalOpen}
        onClose={onPublishModalClose}
        onPublish={async () => {
          if (!formMethods) return;

          const formData = formMethods.getValues();

          let isValid = false;
          try {
            isValid = await formMethods.trigger();

            if (isValid) {
              const cleanedFormData = cleanForPublish(formData);
              await publishHandler(cleanedFormData);

              toast({
                title: 'Plan Design published successfully',
                status: 'success',
                duration: 5000,
                isClosable: true,
              });

              onPublishModalClose();
            } else {
              toast({
                title: 'Validation Error',
                description:
                  'Errors found during validation. Please fix the errors and try again.',
                status: 'error',
                duration: 5000,
                isClosable: true,
              });
            }
          } catch (error: any) {
            console.error('Publishing failed:', error);
            validationContext?.refetch();
            saveHandler(formData);
            toast({
              title: 'Publishing Failed',
              description:
                error?.message ||
                'An unexpected error occurred while publishing. Please try again.',
              status: 'error',
              duration: 5000,
              isClosable: true,
            });
          }
        }}
        isPublishDisabled={!isDirty}
      />
    </Tabs>
  );
};

export default TabSection;
