/**
 * Custom hook for managing item operations (add, edit, delete, save)
 */
import { useDisclosure, useToast } from '@chakra-ui/react';
import get from 'lodash/get';
import set from 'lodash/set';
import { useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

import {
  ItemOperations,
  ItemOperationsProps,
  ItemState,
} from '../../types/types';
import { useLastUpdated } from '../useLastUpdated';

/**
 * URL utilities for managing URL parameters
 */
export const urlUtils = {
  getParams: (): {
    itemIndexParam: string | null;
    titleParam: string | null;
  } => {
    if (typeof window === 'undefined')
      return { itemIndexParam: null, titleParam: null };

    const urlParams = new URLSearchParams(window.location.search);
    return {
      itemIndexParam: urlParams.get('itemIndex'),
      titleParam: urlParams.get('title'),
    };
  },

  updateParams: (params: Record<string, string | null>): void => {
    if (typeof window === 'undefined') return;

    const currentUrl = new URL(window.location.href);
    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        currentUrl.searchParams.delete(key);
      } else {
        currentUrl.searchParams.set(key, value);
      }
    });
    window.history.replaceState({}, '', currentUrl.toString());
  },
};

/**
 * Hook that provides operations for managing collection items
 */
export const useItemOperations = ({
  formMethods,
  basePath,
  items: rawItems,
  filteredItems: rawFilteredItems,
  useAccordion,
  resetAccordions,
  onAddItem,
  onEditItem,
  onRemoveItem,
  customHandleSaveItem,
  customHandleCloseModal,
  itemAddedToastTitle,
  itemUpdatedToastTitle,
  itemRemovedToastTitle,
  createEmptyObject = false,
  newObjectId,
  copyFieldsFrom,
  addCustomVars,
  onOpenModal,
  filterItems,
}: ItemOperationsProps): ItemOperations => {
  // Modal and alert state management
  const { isOpen, onOpen, onClose } = useDisclosure();

  const items = rawItems || [];
  const filteredItems = rawFilteredItems || [];

  const {
    isOpen: isDeleteAlertOpen,
    onOpen: openDeleteAlert,
    onClose: closeDeleteAlert,
  } = useDisclosure();

  // Item state for tracking current operation (add/edit)
  const [itemState, setItemState] = useState<ItemState>({
    isEditing: false,
    index: null,
  });

  // Reference to track if we've already processed the URL parameter
  const initialLoadProcessed = useRef(false);

  const toast = useToast();
  const { getValues } = formMethods;

  // Use the new lastUpdated hook
  const { createTimestamp } = useLastUpdated();

  // Check for URL parameters on mount
  useEffect(() => {
    // Only process once and only if we have items and createEmptyObject is true
    if (
      !initialLoadProcessed.current &&
      createEmptyObject &&
      items.length > 0
    ) {
      const { itemIndexParam } = urlUtils.getParams();

      if (itemIndexParam !== null) {
        const index = parseInt(itemIndexParam, 10);
        if (!isNaN(index) && index >= 0 && index < items.length) {
          // Only process if the user explicitly clicked Edit or Add
          // Don't automatically open on first load
          setItemState({ isEditing: true, index });
          // Don't automatically open the modal on initial load
          // onOpen(); <- This is removed to prevent auto-opening
        }
      }

      initialLoadProcessed.current = true;
    }
  }, [items.length, createEmptyObject]);

  /**
   * Updates the form with modified items
   */
  const updateFormValues = (updatedItems: any[]) => {
    // Use formMethods.setValue to update the basePath with the new items array
    formMethods.setValue(basePath, updatedItems, {
      shouldValidate: true, // Trigger validation after update
      shouldDirty: true, // Mark the field as dirty
      shouldTouch: true, // Mark the field as touched
    });
  };

  /**
   * Updates the lastUpdated timestamp for a specific item path
   */
  const updateLastUpdatedTimestamp = (itemPath: string) => {
    const timestamp = createTimestamp();
    const lastUpdatedPath = `lastUpdated.${itemPath}`;

    // Set the timestamp in the lastUpdated structure
    formMethods.setValue(lastUpdatedPath, timestamp, {
      shouldValidate: false,
      shouldDirty: true,
      shouldTouch: true,
    });

    return timestamp;
  };

  /**
   * Creates an empty object and adds it to the collection
   */
  const createEmptyObjectItem = () => {
    // Create base empty object without timestamp
    const emptyObject: any = {};

    // Add a UUID if newObjectId is specified
    if (newObjectId) {
      emptyObject[newObjectId] = uuidv4();
    }

    // IMPORTANT: Add any custom variables if specified
    // This is critical for ensuring they're in the initial empty object
    if (addCustomVars && addCustomVars.length > 0) {
      addCustomVars.forEach(([path, value]) => {
        try {
          // Use lodash set to handle nested paths
          set(emptyObject, path, value);
        } catch (error) {
          console.error(
            `Error setting custom variable at path ${path}:`,
            error
          );
        }
      });
    }

    // Copy fields from an existing object if specified
    if (copyFieldsFrom && items.length > 0) {
      // Default to index 0 if not specified
      const sourceIndex =
        copyFieldsFrom.index !== undefined ? copyFieldsFrom.index : 0;

      // Make sure the index is valid
      if (sourceIndex >= 0 && sourceIndex < items.length) {
        const sourceObject = items[sourceIndex];

        if (copyFieldsFrom.fields && copyFieldsFrom.fields.length > 0) {
          // Copy only the specified fields
          copyFieldsFrom.fields.forEach((fieldPath) => {
            try {
              // Use lodash get to handle nested paths
              const value = get(sourceObject, fieldPath);

              // Only copy the value if it exists
              if (value !== undefined) {
                // Use lodash set to handle nested paths in the target
                set(emptyObject, fieldPath, value);
              }
            } catch (error) {
              console.error(`Error copying field ${fieldPath}:`, error);
            }
          });
        } else {
          // If no fields specified, copy the entire object (except lastUpdated)
          Object.keys(sourceObject).forEach((key) => {
            if (
              key !== 'lastUpdated' &&
              (newObjectId === undefined || key !== newObjectId)
            ) {
              // Use deep clone to avoid reference issues
              if (
                typeof sourceObject[key] === 'object' &&
                sourceObject[key] !== null
              ) {
                emptyObject[key] = JSON.parse(
                  JSON.stringify(sourceObject[key])
                );
              } else {
                emptyObject[key] = sourceObject[key];
              }
            }
          });
        }
      }
    }

    // IMPORTANT: Make sure the custom variables aren't overwritten by the copyFieldsFrom
    // Re-apply custom variables to ensure they take precedence
    if (addCustomVars && addCustomVars.length > 0) {
      addCustomVars.forEach(([path, value]) => {
        try {
          // Re-apply custom variables to ensure they're properly set
          set(emptyObject, path, value);
        } catch (error) {
          console.error(
            `Error re-setting custom variable at path ${path}:`,
            error
          );
        }
      });
    }

    const updatedItems = [...items, emptyObject];
    updateFormValues(updatedItems);

    // Update the lastUpdated timestamp for the new item
    const newIndex = updatedItems.length - 1;
    const itemPath = `${basePath}[${newIndex}]`;
    updateLastUpdatedTimestamp(itemPath);

    // Update URL parameter with the new index
    urlUtils.updateParams({ itemIndex: newIndex.toString() });

    return newIndex;
  };

  /**
   * Opens the modal to add a new item or runs custom handler if provided
   */
  const handleAddItem = () => {
    if (onAddItem) {
      // Call the provided onAddItem function
      onAddItem();

      // Create empty object and update URL if createEmptyObject is true
      if (createEmptyObject) {
        const newIndex = createEmptyObjectItem();
        // Update URL parameter with the new index
        urlUtils.updateParams({ itemIndex: newIndex.toString() });
        // Set item state to editing mode with the new index
        setItemState({ isEditing: true, index: newIndex });
        onOpen();
      }
    } else if (createEmptyObject) {
      const newIndex = createEmptyObjectItem();
      handleEditItem(newIndex);
    } else {
      setItemState({ isEditing: false, index: null });
      onOpen();
    }
  };

  /**
   * Opens the modal to edit an existing item or runs custom handler if provided
   */
  const handleEditItem = (filteredIndex: number) => {
    let realIndex = filteredIndex;

    // If we have filtered items, need to map from filtered index to real index
    if (filteredItems && filteredItems !== items && !onEditItem) {
      const itemToEdit = filteredItems[filteredIndex];
      realIndex = items.findIndex((item) => item === itemToEdit);
      if (realIndex === -1) return; // Item not found
    }

    if (onEditItem) {
      onEditItem(realIndex);
    } else {
      // Update the lastUpdated timestamp when starting an edit
      if (items[realIndex]) {
        // Create a path to the item being edited
        const itemPath = `${basePath}[${realIndex}]`;

        // Update the timestamp for this item path
        updateLastUpdatedTimestamp(itemPath);
      }

      setItemState({ isEditing: true, index: realIndex });

      // Update URL parameter when editing
      if (createEmptyObject) {
        urlUtils.updateParams({ itemIndex: realIndex.toString() });
      }
    }
    onOpen();
  };

  /**
   * Closes the modal and resets the item state or runs custom handler if provided
   */
  const handleCloseModal = () => {
    if (customHandleCloseModal) {
      customHandleCloseModal();
    } else {
      onClose();
      setItemState({ isEditing: false, index: null });

      // Clear URL parameter when closing modal
      if (createEmptyObject) {
        urlUtils.updateParams({ itemIndex: null });
      }

      // Reset accordions when closing modal to ensure
      // any new or updated item gets proper attention
      if (useAccordion) {
        resetAccordions();
      }
    }
  };

  /**
   * Opens the delete confirmation dialog for an item or runs custom handler if provided
   */
  const handleOpenDeleteDialog = (filteredIndex: number) => {
    // If we have filtered items, need to map from filtered index to real index
    if (filteredItems && filteredItems !== items) {
      // Find the actual item from the filtered items
      const itemToDelete = filteredItems[filteredIndex];

      // Find the real index of this item in the original array
      const realIndex = items.findIndex((item) => item === itemToDelete);

      if (realIndex !== -1) {
        setItemState({ isEditing: true, index: realIndex });
        openDeleteAlert();
      }
    } else {
      // No filtering, use the index directly
      setItemState({ isEditing: true, index: filteredIndex });
      openDeleteAlert();
    }
  };

  /**
   * Confirms deletion of the current item
   */
  const handleConfirmDelete = () => {
    if (itemState.index !== null) {
      if (onRemoveItem) {
        // Use the custom remove function if provided
        onRemoveItem(itemState.index);
      } else {
        const currentItems = [...items];
        // Remove the item from the array
        currentItems.splice(itemState.index, 1);

        // Get the current form values
        const formValues = getValues();

        // Create a clean copy of the form values
        const cleanValues = { ...formValues };

        // Update the items array in the clean values
        set(cleanValues, basePath, currentItems);

        // Reset the form with the clean values to ensure complete cleanup
        // This avoids any issues with stale nested fields
        formMethods.reset(cleanValues);

        toast({
          title: itemRemovedToastTitle,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        // Clear URL parameter if the item being edited is deleted
        if (createEmptyObject) {
          urlUtils.updateParams({ itemIndex: null });
        }
      }
    }
    closeDeleteAlert();
  };

  /**
   * Saves the current item (new or edited) or runs custom handler if provided
   */
  const handleSaveItem = (data: any) => {
    if (customHandleSaveItem) {
      customHandleSaveItem(data);
      return;
    }

    const { isEditing, index } = itemState;

    // Create a copy of the data without the lastUpdated property
    const updatedItem = { ...data };

    // Remove lastUpdated if it exists on the object
    if (updatedItem.lastUpdated) {
      delete updatedItem.lastUpdated;
    }

    // Ensure the UUID is preserved for new items
    if (!isEditing && newObjectId && !updatedItem[newObjectId]) {
      updatedItem[newObjectId] = uuidv4();
    }

    // Add any custom variables for new items
    if (!isEditing && addCustomVars && addCustomVars.length > 0) {
      addCustomVars.forEach(([path, value]) => {
        try {
          // Use lodash set to handle nested paths
          set(updatedItem, path, value);
        } catch (error) {
          console.error(
            `Error setting custom variable at path ${path}:`,
            error
          );
        }
      });
    }

    let updatedItems: any[];
    let itemPath: string;

    if (!isEditing) {
      // Adding new item
      updatedItems = [...items, updatedItem];
      itemPath = `${basePath}[${updatedItems.length - 1}]`;

      updateFormValues(updatedItems);
      updateLastUpdatedTimestamp(itemPath);

      toast({
        title: itemAddedToastTitle,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } else if (index !== null) {
      // Editing existing item
      updatedItems = [...items];
      updatedItems[index] = updatedItem;
      itemPath = `${basePath}[${index}]`;

      updateFormValues(updatedItems);
      updateLastUpdatedTimestamp(itemPath);

      toast({
        title: itemUpdatedToastTitle,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } else {
      return; // Should not happen - index is null in edit mode
    }

    handleCloseModal();
  };

  return {
    itemState,
    handleAddItem,
    handleEditItem,
    handleCloseModal,
    handleOpenDeleteDialog,
    handleConfirmDelete,
    handleSaveItem,
    // These are returned from useDisclosure but we need to expose them
    isOpen,
    onOpen,
    onClose,
    isDeleteAlertOpen,
    openDeleteAlert,
    closeDeleteAlert,
  };
};

export default useItemOperations;
