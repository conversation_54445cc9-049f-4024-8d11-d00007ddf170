// Add this to a new file like parameterUtils.ts

import { UrlParamsConfig } from '../../Models/types';

/**
 * Creates a URL parameter configuration that preserves specified parameters
 *
 * @param paramNames Array of parameter names to preserve during navigation
 * @returns URL parameter configuration object
 */
export function getPathParams(paramNames: string[]): UrlParamsConfig {
  return {
    preserveParams: paramNames,
  };
}

/**
 * Creates a configuration to preserve multiple specific parameters
 *
 * @param params Array of parameter names to preserve
 * @returns URL parameter configuration
 */
export function createParamConfig(params: string[]): UrlParamsConfig {
  return {
    preserveParams: params,
  };
}

/**
 * Creates a configuration with a custom parameter transformation function
 *
 * @param transformFn Function to transform URL parameters
 * @returns URL parameter configuration with custom transformation
 */
export function createCustomParamConfig(
  transformFn: (params: URLSearchParams) => URLSearchParams
): UrlParamsConfig {
  return {
    transformParams: transformFn,
  };
}

/**
 * Utility function to extract specified parameters from a URL
 *
 * @param params URL search params to extract from
 * @param paramNames Array of parameter names to extract
 * @returns New URLSearchParams containing only the specified parameters
 */
export function extractParams(
  params: URLSearchParams,
  paramNames: string[]
): URLSearchParams {
  const extracted = new URLSearchParams();

  paramNames.forEach((name) => {
    const value = params.get(name);
    if (value !== null) {
      extracted.set(name, value);
    }
  });

  return extracted;
}

/**
 * Apply parameter configuration to URL parameters
 *
 * @param currentParams Current URL parameters
 * @param config Parameter configuration
 * @returns Transformed URL parameters
 */
export function applyParamConfig(
  currentParams: URLSearchParams,
  config?: UrlParamsConfig
): URLSearchParams {
  if (!config) {
    return new URLSearchParams();
  }

  // If there's a custom transform function, use it
  if (config.transformParams) {
    return config.transformParams(currentParams);
  }

  // If there are params to preserve, extract them
  if (config.preserveParams && config.preserveParams.length > 0) {
    return extractParams(currentParams, config.preserveParams);
  }

  // Default: preserve no parameters
  return new URLSearchParams();
}

export function getUrlParameterValue(paramName: string): string | null {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(paramName);
}

// /utils/urlUtils.ts
/**
 * URL Parameter Utilities
 */

export function getURLParameter<T>(
  paramName: string,
  parser: (value: string) => T | undefined,
  defaultValue?: T
): T | undefined {
  try {
    if (typeof window === 'undefined') {
      return defaultValue;
    }
    const urlParams = new URLSearchParams(window.location.search);
    const paramValue = urlParams.get(paramName);
    if (paramValue !== null) {
      const parsedValue = parser(paramValue);
      if (parsedValue !== undefined) {
        return parsedValue;
      }
    }
  } catch (error) {
    console.error(`Error reading URL parameter '${paramName}':`, error);
  }
  return defaultValue;
}

export function parseIntParameter(value: string): number | undefined {
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? undefined : parsed;
}

export function parseBooleanParameter(value: string): boolean | undefined {
  if (value.toLowerCase() === 'true') return true;
  if (value.toLowerCase() === 'false') return false;
  return undefined;
}

export function getIndexFromURL(): number | undefined {
  return getURLParameter<number>('index', parseIntParameter, 0);
}

export function shouldUpdateAll(): boolean {
  return getIndexFromURL() === undefined;
}

export function getTrueIndexFromURL(): number | undefined {
  return getURLParameter<number>('index', parseIntParameter);
}

export function updateURLParameter(
  paramName: string,
  value: string | number | undefined
): void {
  const url = new URL(window.location.href);
  if (value === undefined) {
    url.searchParams.delete(paramName);
  } else {
    url.searchParams.set(paramName, value.toString());
  }
  window.history.replaceState({}, '', url.toString());
}
