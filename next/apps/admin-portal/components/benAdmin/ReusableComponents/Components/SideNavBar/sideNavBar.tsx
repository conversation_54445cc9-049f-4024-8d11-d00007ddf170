'use client';
import { ChevronDownIcon, ChevronUpIcon } from '@chakra-ui/icons';
import { Box, Divider, Flex, Icon, Text, VStack } from '@chakra-ui/react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { PLAN_DESIGN_LIST_ITEM } from '../../../organization/components/ChangeRequestIntake/Navigation/navigationConstants';
import { useItemValidationStatus } from '../../../organization/hooks/ChangeRequestIntake/useItemValidationStatus';
import { MenuItem, SideNavigationBarProps } from '../../Models/types';
import { getTrueIndexFromURL } from './parameterUtils';

// Shared styles (unchanged)
const styles = {
  sidebar: {
    borderRight: '1px solid #E5E7EB',
    boxShadow: '2px 0px 5px rgba(0, 0, 0, 0.05)',
    height: '100vh',
    overflowY: 'auto',
  },
  text: {
    color: '#aeaeae',
    fontSize: 'md',
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  item: {
    paddingY: 2,
    paddingX: 6,
    borderRadius: 'md',
    margin: 1,
    cursor: 'pointer',
  },
  active: {
    bg: '#E6F3EE',
    color: '#00894D',
    border: '2px solid #00894D',
    fontWeight: '600',
  },
  inactive: {
    bg: 'transparent',
    color: '#6B7280',
    border: 'transparent',
    fontWeight: '500',
  },
  icon: {
    size: '15px',
    marginRight: 3,
  },
  statusIcon: {
    size: '20px',
    validCheckSize: '30px',
    marginRight: 2,
  },
  dropdownItem: {
    paddingY: 3,
    paddingX: 6,
    paddingLeft: 8,
  },
  divider: {
    borderColor: '#d4d4d4',
    borderWidth: '1px',
  },
};

// Custom hook to dynamically shrink text (unchanged)
const useShrinkText = (text: string) => {
  const textRef = useRef<HTMLParagraphElement>(null);
  const [fontSize, setFontSize] = useState(16);

  useEffect(() => {
    const adjustFontSize = () => {
      const element = textRef.current;
      if (!element) return;

      const containerWidth = element.parentElement?.clientWidth || 0;
      let currentFontSize = 16;
      element.style.fontSize = `${currentFontSize}px`;

      while (element.scrollWidth > containerWidth && currentFontSize > 10) {
        currentFontSize -= 1;
        element.style.fontSize = `${currentFontSize}px`;
      }
      setFontSize(currentFontSize);
    };

    adjustFontSize();
    const resizeHandler = () => requestAnimationFrame(adjustFontSize);
    window.addEventListener('resize', resizeHandler);

    return () => window.removeEventListener('resize', resizeHandler);
  }, [text]);

  return { textRef, fontSize };
};

// Shrinkable text component (unchanged)
const ShrinkText: React.FC<{
  text: string;
  additionalProps?: Record<string, any>;
}> = ({ text, additionalProps }) => {
  const { textRef, fontSize } = useShrinkText(text);
  return (
    <Text
      ref={textRef}
      whiteSpace="nowrap"
      overflow="hidden"
      fontSize={`${fontSize}px`}
      {...additionalProps}
    >
      {text}
    </Text>
  );
};

// Dropdown Item Component (Updated)
const DropdownItem: React.FC<{
  item: MenuItem;
  handleClick: (item: MenuItem, e: React.MouseEvent, isNested: boolean) => void;
  isActive: boolean;
  formMethods: UseFormReturn<any>;
}> = ({ item, handleClick, isActive, formMethods }) => {
  // Determine the appropriate style based on active and disabled state
  const dropdownStyles = item.disabled
    ? { ...styles.inactive, color: '#c0c0c0', cursor: 'not-allowed' }
    : isActive
    ? styles.active
    : styles.inactive;
  const planDesignIndex = getTrueIndexFromURL();

  const { statusIcon, sectionStatus } = useItemValidationStatus(
    item.id,
    isActive,
    planDesignIndex,
    formMethods
  );

  return (
    <Box
      as="a"
      onClick={(e: any) => {
        // Only process the click if the item is not disabled
        if (!item.disabled) {
          handleClick(item, e, true);
        }
      }}
      display="block"
      _focus={{ outline: 'none', boxShadow: 'none' }}
      _focusVisible={{ outline: 'none', boxShadow: 'none' }}
      style={{ userSelect: 'none' }}
      _hover={{ cursor: item.disabled ? 'not-allowed' : 'pointer' }}
    >
      <Flex
        py={styles.item.paddingY}
        px={styles.item.paddingX}
        borderRadius={styles.item.borderRadius}
        {...dropdownStyles}
        m={styles.item.margin}
        opacity={item.disabled ? 0.6 : 1}
      >
        {statusIcon && (
          <Icon
            as={statusIcon}
            mr={styles.statusIcon.marginRight}
            fontSize={
              sectionStatus === 'success'
                ? styles.statusIcon.validCheckSize
                : styles.statusIcon.size
            }
            color={
              sectionStatus === 'error'
                ? 'red.500'
                : sectionStatus === 'warning'
                ? 'orange.400'
                : sectionStatus === 'success'
                ? '#00894D'
                : dropdownStyles.color
            }
          />
        )}
        <ShrinkText
          text={item.label}
          additionalProps={{ fontWeight: dropdownStyles.fontWeight }}
        />
      </Flex>
    </Box>
  );
};

// Sidebar Item Component (Updated)
const SidebarItem: React.FC<{
  item: MenuItem;
  isActive: boolean;
  isOpen: boolean;
  handleClick: (item: MenuItem, e: React.MouseEvent, isNested: boolean) => void;
  activeItemId?: string;
  formMethods: UseFormReturn<any>;
}> = ({ item, isActive, handleClick, isOpen, activeItemId, formMethods }) => {
  // Determine the appropriate style based on active and disabled state
  const itemStyles = item.disabled
    ? { ...styles.inactive, color: '#c0c0c0', cursor: 'not-allowed' }
    : isActive
    ? styles.active
    : styles.inactive;

  const isPlanDesignListActive = activeItemId === PLAN_DESIGN_LIST_ITEM;

  const planDesignIndex = isPlanDesignListActive
    ? undefined
    : getTrueIndexFromURL();
  const { statusIcon, sectionStatus } = useItemValidationStatus(
    item.id,
    isActive,
    planDesignIndex,
    formMethods
  );

  return (
    <Box w="full">
      <Box
        as="a"
        onClick={(e: any) => {
          // Only process the click if the item is not disabled
          if (!item.disabled) {
            handleClick(item, e, false);
          }
        }}
        display="block"
        _hover={{ cursor: item.disabled ? 'not-allowed' : 'pointer' }}
        _focus={{ outline: 'none', boxShadow: 'none' }}
        _focusVisible={{ outline: 'none', boxShadow: 'none' }}
        style={{ userSelect: 'none' }}
      >
        <Flex
          align="center"
          py={styles.item.paddingY}
          px={styles.item.paddingX}
          borderRadius={styles.item.borderRadius}
          {...itemStyles}
          m={styles.item.margin}
          opacity={item.disabled ? 0.6 : 1}
        >
          {item.icon && (
            <Icon
              as={item.icon}
              mr={styles.icon.marginRight}
              fontSize={styles.icon.size}
              color={itemStyles.color}
            />
          )}
          {!isPlanDesignListActive && statusIcon && (
            <Icon
              as={statusIcon}
              mr={styles.statusIcon.marginRight}
              fontSize={
                sectionStatus === 'success'
                  ? styles.statusIcon.validCheckSize
                  : styles.statusIcon.size
              }
              color={
                sectionStatus === 'error'
                  ? 'red.500'
                  : sectionStatus === 'warning'
                  ? 'orange.400'
                  : sectionStatus === 'success'
                  ? '#00894D'
                  : itemStyles.color
              }
            />
          )}

          <ShrinkText
            text={item.label}
            additionalProps={{ flex: 1, fontWeight: itemStyles.fontWeight }}
          />
          {item.hasDropdown && (
            <Icon
              as={isOpen ? ChevronUpIcon : ChevronDownIcon}
              color={styles.inactive.color}
            />
          )}
        </Flex>
      </Box>

      {/* Render dropdown items */}
      {item.hasDropdown && isOpen && item.dropdownItems && (
        <VStack
          align="start"
          spacing={0}
          pl={styles.dropdownItem.paddingLeft}
          w="full"
        >
          {item.dropdownItems.map((dropdownItem) => (
            <DropdownItem
              key={dropdownItem.id}
              item={dropdownItem}
              handleClick={handleClick}
              isActive={dropdownItem.id === activeItemId}
              formMethods={formMethods}
            />
          ))}
        </VStack>
      )}
    </Box>
  );
};

// Main Sidebar Component (Updated with Fix)
export const SideNavigationBar: React.FC<SideNavigationBarProps> = ({
  config,
  onSelectItem,
  activeItemId,
  formMethods,
}) => {
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // Find which dropdown contains the active item (if any)
  useEffect(() => {
    // Start with all dropdowns closed
    let dropdownToOpen: string | null = null;

    if (activeItemId) {
      // Check if active item is in any dropdown
      for (const section of config.sections) {
        for (const item of section.items) {
          if (item.hasDropdown && item.dropdownItems) {
            // If active item is in this dropdown's items, mark this dropdown to be opened
            const activeItemInDropdown = item.dropdownItems.some(
              (dropdownItem) => dropdownItem.id === activeItemId
            );

            if (activeItemInDropdown) {
              dropdownToOpen = item.id;
              break;
            }
          }
        }
        if (dropdownToOpen) break;
      }
    }

    // Set the openDropdownId - either to the dropdown containing the active item, or null
    setOpenDropdownId(dropdownToOpen);
  }, [activeItemId, config.sections]);

  const handleItemClick = useCallback(
    (item: MenuItem, e: React.MouseEvent, isNested = false) => {
      e.preventDefault();

      // Skip processing if the item is disabled
      if (item.disabled) return;

      if (item.hasDropdown) {
        // Toggle this dropdown open/closed
        setOpenDropdownId((prevId) => (prevId === item.id ? null : item.id));
      } else {
        if (!isNested) {
          // Only close dropdowns if it's a top-level non-dropdown item
          setOpenDropdownId(null);
        }
        // Select the item regardless of whether it's nested
        onSelectItem?.(item);
      }
    },
    [onSelectItem]
  );

  return (
    <VStack align="start" spacing={0} w="full">
      {config.sections.map((section, sectionIndex) => (
        <React.Fragment key={sectionIndex}>
          {section.title && (
            <>
              <Box px={6} py={3} w="full">
                <ShrinkText
                  text={section.title}
                  additionalProps={styles.text}
                />
              </Box>
              <Divider
                borderColor={styles.divider.borderColor}
                borderWidth={styles.divider.borderWidth}
              />
            </>
          )}

          {section.items.map((item) => {
            const isOpen = item.id === openDropdownId;
            return (
              <SidebarItem
                key={item.id}
                item={item}
                isActive={activeItemId === item.id}
                isOpen={isOpen}
                handleClick={handleItemClick}
                activeItemId={activeItemId}
                formMethods={formMethods}
              />
            );
          })}

          {sectionIndex < config.sections.length - 1 && (
            <Divider
              borderColor={styles.divider.borderColor}
              borderWidth={styles.divider.borderWidth}
            />
          )}
        </React.Fragment>
      ))}
    </VStack>
  );
};
