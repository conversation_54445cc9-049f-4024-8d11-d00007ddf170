import { getTrueIndexFromURL } from './parameterUtils';

/**
 * Filters an array of items (with a 'field' property) to only include those
 * that match the current plan design index from the URL.
 * If the item does not have a 'field' property, returns the item (for ValidationRule[]).
 */

export function filterByPlanDesignField<T extends { field: string }>(
  items: T[],
  planDesignIndex?: number
): T[] {
  const index = planDesignIndex ?? getTrueIndexFromURL();
  if (index === undefined) return items;
  const pattern = new RegExp(`plan_designs(?:\\.|\\[)${index}(?:\\]|\\.)`);
  return items.filter((item) => pattern.test(item.field));
}

/**
 * Normalizes a field path from API to match Form's field names.
 *
 * @param fieldPath - The field path from the validation API (e.g., "plan_designs.0.plan_design_details.0.effective_date").
 * @param addPlanPrefix - Whether to prefix the path with "plan." (default: false).
 * @returns A normalized field path (e.g., "plan.plan_designs.0.plan_design_details.[0].effective_date").
 */
export function normalizePlanDesignFieldPath(
  fieldPath: string,
  addPlanPrefix = false
): string {
  const prefixed = addPlanPrefix ? `plan.${fieldPath}` : fieldPath;

  // Replace plan_design_details.X with plan_design_details.[X]
  const convertedDetails = prefixed.replace(
    /plan_design_details\.(\d+)/g,
    'plan_design_details.[$1]'
  );

  // Replace plan_design_detail_esi.X with plan_design_detail_esi.[X]
  const convertedEsi = convertedDetails.replace(
    /plan_design_detail_esi\.(\d+)/g,
    'plan_design_detail_esi.[$1]'
  );

  return convertedEsi;
}
