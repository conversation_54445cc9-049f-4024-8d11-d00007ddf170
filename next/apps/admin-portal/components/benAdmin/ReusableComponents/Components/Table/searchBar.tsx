import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Icon,
  Input,
  InputGroup,
  InputRightElement,
  Tooltip,
} from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';
import { FaRegQuestionCircle, FaTimes } from 'react-icons/fa';

import { searchBarStyles } from '../../Styles/styles';

export type QuickSearchOption<T> = {
  label: string;
  filterFunction: (data: T[]) => T[];
  active?: boolean;
};

type SearchBarProps<T> = {
  data: T[];
  placeholder: string;
  searchField: keyof T;
  label?: string;
  hint?: string;
  disabled?: boolean;
  quickSearchOptions?: QuickSearchOption<T>[];
  defaultQuickFilter?: string;
  onSearch: (filteredData: T[]) => void;
};

export function SearchBar<T>({
  data,
  placeholder,
  searchField,
  label,
  hint,
  disabled = false,
  quickSearchOptions,
  onSearch,
}: SearchBarProps<T>) {
  // Find the default active quick filter during initialization
  const defaultActiveOption = quickSearchOptions?.find(
    (option) => option.active
  );

  const [query, setQuery] = useState('');
  const [activeQuickFilter, setActiveQuickFilter] = useState<string | null>(
    null
  );

  // Apply filters based on quick filter and search query
  const applyFilters = (newQuery: string, baseData: T[]) => {
    const lowerCaseQuery = newQuery.trim().toLowerCase();
    if (!lowerCaseQuery) {
      // If query is empty, return only the filtered data from quick filter
      onSearch(baseData);
      return;
    }

    // Apply search query on top of the base data (from quick filter or full data)
    const searchResults = baseData.filter((item) =>
      String(item[searchField]).toLowerCase().includes(lowerCaseQuery)
    );

    onSearch(searchResults);
  };

  // Handle search input changes dynamically
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);

    const baseData =
      activeQuickFilter && quickSearchOptions
        ? quickSearchOptions
            .find((option) => option.label === activeQuickFilter)
            ?.filterFunction(data) ?? data
        : data;

    applyFilters(newQuery, baseData);
  };

  // Handle quick filter activation/deactivation
  const handleQuickFilter = (
    label: string,
    filterFunction: (data: T[]) => T[]
  ) => {
    if (activeQuickFilter === label) {
      // Deselect the active filter if clicked again
      setActiveQuickFilter(null);
      applyFilters(query, data); // Apply only search query if present, no filter
    } else {
      // Activate new filter
      setActiveQuickFilter(label);
      const filtered = filterFunction(data);
      applyFilters(query, filtered); // Apply search query on top of new filter
    }
  };

  // Handle clearing the search query and resetting the data
  const handleClearSearch = () => {
    setQuery('');
    const baseData =
      activeQuickFilter && quickSearchOptions
        ? quickSearchOptions
            .find((option) => option.label === activeQuickFilter)
            ?.filterFunction(data) ?? data
        : data;

    onSearch(baseData); // Reset to current quick filter or full data
  };

  // Apply default quick filter if none is active and no query is present
  useEffect(() => {
    if (
      !activeQuickFilter &&
      !query.trim() &&
      defaultActiveOption &&
      data.length > 0
    ) {
      handleQuickFilter(
        defaultActiveOption.label,
        defaultActiveOption.filterFunction
      );
    }
  }, [activeQuickFilter, query, defaultActiveOption, data, onSearch]);

  return (
    <FormControl {...searchBarStyles.formControl}>
      {/* Label and Hint */}
      <Flex justify="space-between" align="center" mb={2}>
        <FormLabel {...searchBarStyles.label}>{label || 'Search'}</FormLabel>
        {hint && (
          <Tooltip label={hint} fontSize="sm" placement="top" hasArrow>
            <Box as="span" cursor="help">
              <Icon as={FaRegQuestionCircle} {...searchBarStyles.hintIcon} />
            </Box>
          </Tooltip>
        )}
      </Flex>

      {/* Input and Search Button */}
      <Flex {...searchBarStyles.inputContainer}>
        <InputGroup>
          <Input
            placeholder={placeholder}
            value={query}
            onChange={handleInputChange}
            isDisabled={disabled}
            {...searchBarStyles.input}
          />
          {query && (
            <InputRightElement display="flex" alignItems="center" h="100%">
              <Icon
                as={FaTimes}
                cursor="pointer"
                color="gray.500"
                onClick={handleClearSearch}
              />
            </InputRightElement>
          )}
        </InputGroup>
      </Flex>

      {/* Quick Filters */}
      {quickSearchOptions && quickSearchOptions.length > 0 && (
        <Box {...searchBarStyles.quickFilterContainer}>
          {quickSearchOptions.map(({ label, filterFunction }) => (
            <Button
              key={label}
              {...searchBarStyles.quickFilterButton}
              {...(activeQuickFilter === label
                ? searchBarStyles.activeQuickFilterButton
                : {})}
              onClick={() => handleQuickFilter(label, filterFunction)}
            >
              {label}
            </Button>
          ))}
        </Box>
      )}
    </FormControl>
  );
}
