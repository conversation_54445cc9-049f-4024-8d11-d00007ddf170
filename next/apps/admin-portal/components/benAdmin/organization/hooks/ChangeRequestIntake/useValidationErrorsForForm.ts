import { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { ValidationResponse } from '../../../Models/interfaces';
import { SubCategoryType } from '../../../ReusableComponents/Models/types';
import { ValidationFieldMapper } from '../../components/ChangeRequestIntake/MenuItems/PlanDesignTool/inlineValidations';

export function useValidationErrorsForForm({
  subCategories,
  formMethods,
  validationData,
  contextIndex,
}: {
  subCategories: SubCategoryType[];
  formMethods: UseFormReturn<any>;
  validationData: ValidationResponse | null;
  contextIndex: number | null | undefined;
}) {
  const fieldPaths =
    ValidationFieldMapper.extractFieldNamesFromSubCategories(subCategories);
  const fieldPathMap =
    ValidationFieldMapper.createNormalizedPathMap(fieldPaths);

  useEffect(() => {
    if (!contextIndex) return;

    const currentPageValidation = validationData?.results?.[contextIndex];
    if (!currentPageValidation) return;

    const { errors = [], warnings = [] } = currentPageValidation;

    errors.forEach(({ field, message }) => {
      const resolvedPath = ValidationFieldMapper.resolveField(
        field,
        fieldPathMap
      );

      if (resolvedPath) {
        formMethods.setError(resolvedPath, {
          type: ValidationFieldMapper.error,
          message,
        });
      }
    });

    warnings.forEach(({ field, message }) => {
      const resolvedPath = ValidationFieldMapper.resolveField(
        field,
        fieldPathMap
      );

      if (resolvedPath) {
        formMethods.setError(resolvedPath, {
          type: ValidationFieldMapper.warning,
          message,
        });
      }
    });
  }, [validationData, formMethods, contextIndex, fieldPathMap]);
}
