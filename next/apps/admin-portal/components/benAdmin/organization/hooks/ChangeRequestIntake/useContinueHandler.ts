import { useToast } from '@chakra-ui/react';
import { useSearchParams } from 'next/navigation';
import { useCallback, useRef } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { FieldValidations } from '../../..';
import { filterByPlanDesignField } from '../../../ReusableComponents/Components/SideNavBar/filterUtils';
import { filterValidationByProductType } from '../../components/ChangeRequestIntake/MenuItems/PlanDesignTool/ProductsAndServices/genericProductComponent';
import { getUIContextFromNavigationConstant } from '../../components/ChangeRequestIntake/Navigation/uiContextEnum';
import { useSetPageInitialized } from '../../components/ChangeRequestIntake/Validations/ValidationContext';
import { useSaveChangeRequestHandler } from '../useOrganizationHooks';

interface UseContinueHandlerProps {
  refetch: () => Promise<any>;
  onUpdateActiveItem?: (id: string) => void;
  formMethods: UseFormReturn<any>;
}

export const useContinueHandler = ({
  refetch,
  onUpdateActiveItem,
  formMethods,
}: UseContinueHandlerProps) => {
  const searchParams = useSearchParams();
  const planDesignIndex = searchParams?.get('index')
    ? parseInt(searchParams.get('index') as string)
    : undefined;
  // Call the hook at the top level
  const saveHandler = useSaveChangeRequestHandler(formMethods, false, true);
  const toast = useToast();
  // Track if warning has been acknowledged for the current page/index
  const warningAcknowledgedRef = useRef<{ [key: string]: boolean }>({});

  // Import the reusable setPageInitialized hook
  const setPageInitialized = useSetPageInitialized();

  const createContinueHandler = useCallback(
    (
      page: string | number,
      navigateTo: string,
      contextParamName?: string,
      productType?: string
    ) => {
      return async () => {
        // Save current form data before validation
        const currentValues = formMethods.watch();
        const uiContextInd =
          typeof page === 'number'
            ? page
            : getUIContextFromNavigationConstant(page);

        await saveHandler(currentValues);

        try {
          // Force a fresh validation fetch (bypasses cache and gets latest data from API)
          // This is the ONLY time validation is refetched after the initial page load
          const result = await refetch();
          // Wait a tick for state to update
          await new Promise((resolve) => setTimeout(resolve, 100));

          if (uiContextInd === undefined) {
            console.error(`Could not find UI context for page: ${page}`);
            return;
          }

          // Find the appropriate page result
          let pageResult: any | undefined;
          if (contextParamName) {
            for (const res of Object.values(result?.data?.results || {})) {
              if ((res as any)[contextParamName] === uiContextInd) {
                pageResult = res;
                break;
              }
            }
          } else {
            pageResult = result?.data?.results?.[uiContextInd];
          }

          // Check if we have validation errors and warnings after refetch
          const pageErrors = [
            ...(pageResult?.errors || []),
            ...(pageResult?.validation_results?.errors || []),
          ];

          const pageWarnings = [
            ...(pageResult?.warnings || []),
            ...(pageResult?.validation_results?.warnings || []),
          ];

          let filteredErrors = filterByPlanDesignField(
            pageErrors,
            planDesignIndex
          ) as FieldValidations[];
          let filteredWarnings = filterByPlanDesignField(
            pageWarnings,
            planDesignIndex
          ) as FieldValidations[];

          // If productType is provided, filter errors/warnings by productType using utility
          if (productType) {
            filteredErrors = filterValidationByProductType(
              filteredErrors,
              formMethods,
              productType,
              planDesignIndex
            );
            filteredWarnings = filterValidationByProductType(
              filteredWarnings,
              formMethods,
              productType,
              planDesignIndex
            );
          }

          const hasErrors = filteredErrors.length > 0;
          const hasWarnings = filteredWarnings.length > 0;

          // Update form validations with latest status
          const currentValidations = formMethods.getValues('validations') || {};
          let updateKey = String(uiContextInd);
          if (productType) {
            updateKey = productType
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/(^-|-$)/g, '');
          }

          if (!currentValidations[updateKey]) {
            currentValidations[updateKey] = {
              initialized: [],
              success: [],
              warning: [],
            };
          }

          const validationState = currentValidations[updateKey];

          if (Array.isArray(validationState.initialized)) {
            // Array-based validation
            const numPlans =
              formMethods.getValues('plan.plan_designs')?.length || 1;
            // Initialize arrays if needed
            while (validationState.initialized.length < numPlans) {
              validationState.initialized.push(false);
              validationState.success.push(false);
              validationState.warning.push(false);
            }

            if (productType) {
              // Set all for product types
              for (let i = 0; i < numPlans; i++) {
                validationState.initialized[i] = true;
                validationState.success[i] = !hasErrors;
                validationState.warning[i] = hasWarnings && !hasErrors;
              }
            } else if (typeof planDesignIndex === 'number') {
              // Set specific index
              validationState.initialized[planDesignIndex] = true;
              validationState.success[planDesignIndex] = !hasErrors;
              validationState.warning[planDesignIndex] =
                hasWarnings && !hasErrors;
            }
          } else {
            // Simple page
            validationState.initialized = true;
            validationState.success = !hasErrors;
            validationState.warning = hasWarnings && !hasErrors;
          }

          formMethods.setValue('validations', currentValidations, {
            shouldValidate: false,
            shouldDirty: false,
          });

          const warningKey = `${page}-${uiContextInd}`;

          if (filteredWarnings.length > 0 && onUpdateActiveItem) {
            if (!warningAcknowledgedRef.current[warningKey]) {
              toast({
                title: 'Warning',
                description: filteredWarnings[0].message,
                status: 'warning',
                duration: 5000,
                isClosable: true,
              });
              warningAcknowledgedRef.current[warningKey] = true;
              onUpdateActiveItem(navigateTo);
            }
          } else {
            // Reset acknowledgement if no warnings
            warningAcknowledgedRef.current[warningKey] = false;
          }

          if (filteredErrors.length > 0) {
            toast({
              title: 'Error',
              description:
                filteredErrors[0].message ||
                'Please fix page validation errors',
              status: 'error',
              duration: 5000,
              isClosable: true,
            });
          }

          if (!hasErrors && onUpdateActiveItem) {
            onUpdateActiveItem(navigateTo);
          }
        } catch (error) {
          console.error('Error during refetch:', error);
        } finally {
          setPageInitialized(
            formMethods,
            uiContextInd as number,
            planDesignIndex
          );
          await saveHandler(currentValues);
        }
      };
    },
    [
      refetch,
      onUpdateActiveItem,
      saveHandler,
      formMethods,
      toast,
      planDesignIndex,
      setPageInitialized,
    ]
  );

  return { createContinueHandler };
};
