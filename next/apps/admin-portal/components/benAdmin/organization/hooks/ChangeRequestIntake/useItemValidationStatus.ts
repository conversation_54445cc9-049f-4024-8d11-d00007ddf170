import React, { useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { IoIosCheckmark } from 'react-icons/io';
import { PiWarning, PiWarningOctagonFill } from 'react-icons/pi';

import {
  getUIContextFromNavigationConstant,
  SIMPLE_PAGES,
} from '../../components/ChangeRequestIntake/Navigation/uiContextEnum';

const generateContextIds = (start: number, length: number) =>
  Array.from({ length }, (_, i) => String(start + i));

export const useItemValidationStatus = (
  itemId: string,
  isActive?: boolean,
  planDesignIndex?: number,
  formMethods?: UseFormReturn<any>
) => {
  const [statusIcon, setStatusIcon] = useState<React.ElementType | null>(null);
  const [sectionStatus, setSectionStatus] = useState<string | null>(null);

  // Watch validations outside of useEffect to ensure we get updates
  const validations = formMethods?.watch('validations') || {};

  const parentContextObj: { [key: string]: string[] } = {
    'patient-pay': generateContextIds(14, 4),
    'pharmacy-accumulators': generateContextIds(18, 4),
    'core-products': generateContextIds(22, 2),
  };

  useEffect(() => {
    if (!formMethods) {
      setStatusIcon(null);
      setSectionStatus(null);
      return;
    }

    const isParent = itemId in parentContextObj;

    // --- Handle parent items (aggregating multiple child pages) ---
    if (isParent) {
      const childContexts = parentContextObj[itemId];
      let hasError = false;
      let hasWarning = false;
      let allInitialized = true;
      let anyInitialized = false;

      childContexts.forEach((childKey) => {
        const childState = validations[childKey];

        if (!childState) {
          allInitialized = false;
          return;
        }

        const childNum = Number(childKey);
        if (SIMPLE_PAGES.includes(childNum)) {
          // Simple page logic
          if (childState.initialized) {
            anyInitialized = true;
            if (!childState.success) hasError = true;
            else if (childState.warning) hasWarning = true;
          } else {
            allInitialized = false;
          }
        } else if (
          Array.isArray(childState.initialized) &&
          Array.isArray(childState.success) &&
          Array.isArray(childState.warning)
        ) {
          // Array page logic
          if (typeof planDesignIndex === 'number' && planDesignIndex >= 0) {
            // Check specific index
            if (childState.initialized[planDesignIndex]) {
              anyInitialized = true;
              if (!childState.success[planDesignIndex]) hasError = true;
              else if (childState.warning[planDesignIndex]) hasWarning = true;
            } else {
              allInitialized = false;
            }
          } else {
            // Check all indexes
            const childAnyInitialized = childState.initialized.some(Boolean);
            const childAllInitialized = childState.initialized.every(Boolean);

            if (childAnyInitialized) anyInitialized = true;
            if (!childAllInitialized) allInitialized = false;

            // Check for errors in any initialized index
            const childHasError = childState.initialized.some(
              (init: boolean, i: number) => init && !childState.success[i]
            );
            if (childHasError) hasError = true;

            // Check for warnings in any initialized index (only if success)
            const childHasWarning = childState.initialized.some(
              (init: boolean, i: number) =>
                init && childState.success[i] && childState.warning[i]
            );
            if (childHasWarning) hasWarning = true;
          }
        }
      });

      if (!anyInitialized) {
        setStatusIcon(null);
        setSectionStatus(null);
      } else if (hasError) {
        setStatusIcon(() => PiWarningOctagonFill);
        setSectionStatus('error');
      } else if (hasWarning) {
        setStatusIcon(() => PiWarning);
        setSectionStatus('warning');
      } else if (allInitialized) {
        setStatusIcon(() => IoIosCheckmark);
        setSectionStatus('success');
      } else {
        setStatusIcon(null);
        setSectionStatus(null);
      }
      return;
    }

    // List of sidebar item IDs to skip warnings for (non-error/warning pages)
    const SKIP_IDS = [
      'CLIENT_PROFILE_REVIEW',
      'structure-tool',
      'structure-tool-1',
      'structure-tool-2',
      'pricing-tool',
      'pricing-tool-1',
      'pricing-tool-2',
      'plan-design-tool',
      'overview',
      'contacts',
      'documents',
      'activity',
      'preview',
      'add-on-products',
      // Add any other non-error/warning sidebar IDs here
    ];

    if (SKIP_IDS.includes(itemId)) {
      setStatusIcon(null);
      setSectionStatus(null);
      return;
    }

    const uiContextId = getUIContextFromNavigationConstant(itemId);
    const pageKey =
      typeof uiContextId !== 'undefined' ? String(uiContextId) : itemId;
    const pageState = validations[pageKey];

    // --- Handle array-based pages (12-23) ---
    if (
      pageState &&
      Array.isArray(pageState.initialized) &&
      Array.isArray(pageState.success) &&
      Array.isArray(pageState.warning)
    ) {
      // Case 1: We have a specific planDesignIndex
      if (typeof planDesignIndex === 'number' && planDesignIndex >= 0) {
        // Check only the specific index
        const isInitialized = pageState.initialized[planDesignIndex];
        const isSuccess = pageState.success[planDesignIndex];
        const hasWarning = pageState.warning[planDesignIndex];

        if (!isInitialized) {
          // Not initialized yet, no icon
          setStatusIcon(null);
          setSectionStatus(null);
        } else if (!isSuccess) {
          // Error takes precedence over warning
          setStatusIcon(() => PiWarningOctagonFill);
          setSectionStatus('error');
        } else if (hasWarning) {
          // Success but with warning
          setStatusIcon(() => PiWarning);
          setSectionStatus('warning');
        } else {
          // Success with no warning
          setStatusIcon(() => IoIosCheckmark);
          setSectionStatus('success');
        }
      } else {
        // Case 2: No planDesignIndex, check all indexes
        const anyNotInitialized = pageState.initialized.some(
          (init: boolean) => !init
        );
        const anyError = pageState.initialized.some(
          (init: boolean, i: number) => init && !pageState.success[i]
        );
        const anyWarning = pageState.initialized.some(
          (init: boolean, i: number) =>
            init && pageState.success[i] && pageState.warning[i]
        );
        const allInitialized = pageState.initialized.every(Boolean);
        const allSuccess = pageState.success.every(Boolean);
        const allNoWarning = pageState.warning.every((w: boolean) => !w);

        if (anyNotInitialized) {
          // Not all initialized, no icon
          setStatusIcon(null);
          setSectionStatus(null);
        } else if (anyError) {
          // At least one error
          setStatusIcon(() => PiWarningOctagonFill);
          setSectionStatus('error');
        } else if (anyWarning) {
          // All success but at least one warning
          setStatusIcon(() => PiWarning);
          setSectionStatus('warning');
        } else if (allInitialized && allSuccess && allNoWarning) {
          // All success with no warnings
          setStatusIcon(() => IoIosCheckmark);
          setSectionStatus('success');
        } else {
          // Fallback
          setStatusIcon(null);
          setSectionStatus(null);
        }
      }
      return;
    }

    // --- Handle simple pages (1-11) ---
    if (typeof uiContextId === 'undefined') {
      setStatusIcon(null);
      setSectionStatus(null);
      return;
    }
    const pageNum = Number(uiContextId);
    const simplePageState = validations[pageKey];
    if (!simplePageState) {
      setStatusIcon(null);
      setSectionStatus(null);
      return;
    }

    if (SIMPLE_PAGES.includes(pageNum)) {
      // Simple page logic (pages 1-11)
      if (simplePageState.initialized === true) {
        if (!simplePageState.success) {
          setStatusIcon(() => PiWarningOctagonFill);
          setSectionStatus('error');
        } else if (simplePageState.warning) {
          setStatusIcon(() => PiWarning);
          setSectionStatus('warning');
        } else {
          setStatusIcon(() => IoIosCheckmark);
          setSectionStatus('success');
        }
      } else {
        setStatusIcon(null);
        setSectionStatus(null);
      }
    } else {
      // Non-simple pages without array data - no icon
      setStatusIcon(null);
      setSectionStatus(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [validations, formMethods, itemId, planDesignIndex, isActive]); // Include validations in the dependency array

  return { statusIcon, sectionStatus };
};
