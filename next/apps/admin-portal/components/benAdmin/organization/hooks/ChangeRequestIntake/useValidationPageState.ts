import { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  SIMPLE_PAGES,
  UIContextScreen,
} from '../../components/ChangeRequestIntake/Navigation/uiContextEnum';

interface PageValidationStateWithArray {
  planDesignIndex: number[];
  initialized: boolean[];
  success: boolean[];
  warning: boolean[];
}

interface PageValidationStateSimple {
  planDesignIndex: null;
  initialized: boolean;
  success: boolean;
  warning: boolean;
}

export type { PageValidationStateSimple, PageValidationStateWithArray };

export type ValidationsState = Record<
  string,
  PageValidationStateWithArray | PageValidationStateSimple
>;

// Utility to normalize product_type to kebab-case
function toKebabCase(str: string): string {
  return str
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

/**
 * Initializes and manages the validations state for all UIContextScreen pages.
 * For pages 1-11, planDesignIndex is null and initialized/success are booleans.
 * For 12+, planDesignIndex, initialized, and success are arrays matching plan designs.
 */
export function useValidationPageState(formMethods: UseFormReturn<any>) {
  useEffect(() => {
    const planDesigns = formMethods.getValues('plan.plan_designs') || [];
    const numPlanDesigns = Array.isArray(planDesigns) ? planDesigns.length : 0;
    const planDesignIndices = Array.from(
      { length: numPlanDesigns },
      (_, i) => i
    );

    const currentValidations = formMethods.getValues('validations') || {};
    let updated = false;

    Object.values(UIContextScreen)
      .filter((v) => typeof v === 'number')
      .forEach((enumValue) => {
        const key = String(enumValue);
        const pageNum = Number(enumValue);
        if (!currentValidations[key]) {
          let pageState;
          if (SIMPLE_PAGES.includes(pageNum)) {
            pageState = {
              planDesignIndex: null,
              initialized: false,
              success: false,
              warning: false,
            };
          } else {
            pageState = {
              planDesignIndex: planDesignIndices.slice(),
              initialized: Array(numPlanDesigns).fill(false),
              success: Array(numPlanDesigns).fill(false),
              warning: Array(numPlanDesigns).fill(false),
            };
          }
          currentValidations[key] = pageState;
          updated = true;
        }
      });

    // For existing validations, handle resizing for array pages
    Object.entries(currentValidations).forEach(([key, pageState]) => {
      const pageNum = Number(key);
      if (
        !SIMPLE_PAGES.includes(pageNum) &&
        pageState &&
        typeof pageState === 'object' &&
        'planDesignIndex' in pageState &&
        Array.isArray(pageState.planDesignIndex)
      ) {
        const typedState = pageState as PageValidationStateWithArray;
        const currentLength = typedState.planDesignIndex.length;
        if (currentLength !== numPlanDesigns) {
          // Resize arrays
          typedState.planDesignIndex = planDesignIndices.slice();
          // Extend or truncate initialized and success, preserving existing values
          const newInitialized = Array(numPlanDesigns);
          const newSuccess = Array(numPlanDesigns);
          const newWarning = Array(numPlanDesigns);
          for (let i = 0; i < numPlanDesigns; i++) {
            newInitialized[i] =
              i < currentLength ? typedState.initialized[i] : false;
            newSuccess[i] = i < currentLength ? typedState.success[i] : false;
            newWarning[i] =
              i < currentLength ? typedState.warning?.[i] ?? false : false;
          }
          typedState.initialized = newInitialized;
          typedState.success = newSuccess;
          typedState.warning = newWarning;
          updated = true;
        }
      }
    });

    // Ensure warning exists for all pages
    Object.entries(currentValidations).forEach(([key, pageState]) => {
      const pageNum = Number(key);
      if (SIMPLE_PAGES.includes(pageNum)) {
        const typedState = pageState as PageValidationStateSimple;
        if (
          !('warning' in typedState) ||
          typeof typedState.warning !== 'boolean'
        ) {
          typedState.warning = false;
          updated = true;
        }
      } else if (
        pageState &&
        typeof pageState === 'object' &&
        'planDesignIndex' in pageState &&
        Array.isArray(pageState.planDesignIndex)
      ) {
        const typedState = pageState as PageValidationStateWithArray;
        if (
          !Array.isArray(typedState.warning) ||
          typedState.warning.length !== numPlanDesigns
        ) {
          const newWarning = Array(numPlanDesigns).fill(false);
          // Preserve existing values if possible
          if (Array.isArray(typedState.warning)) {
            for (
              let i = 0;
              i < Math.min(typedState.warning.length, numPlanDesigns);
              i++
            ) {
              newWarning[i] = typedState.warning[i];
            }
          }
          typedState.warning = newWarning;
          updated = true;
        }
      }
    });

    // --- PRODUCT TYPE VALIDATION STATE MANAGEMENT ---
    // Gather all product types from current plan designs
    const allProductTypes = new Set<string>();
    planDesigns.forEach((plan: any) => {
      (plan.plan_design_ancillaries || []).forEach((ancillary: any) => {
        if (ancillary?.product_type)
          allProductTypes.add(toKebabCase(ancillary.product_type));
      });
    });
    // Add missing product types at root
    allProductTypes.forEach((pt) => {
      const normalizedType = toKebabCase(pt);
      if (!currentValidations[normalizedType]) {
        currentValidations[normalizedType] = {
          planDesignIndex: planDesignIndices.slice(),
          initialized: Array(numPlanDesigns).fill(false),
          success: Array(numPlanDesigns).fill(false),
          warning: Array(numPlanDesigns).fill(false),
        };
        updated = true;
      } else {
        // Resize arrays if plan designs changed
        const state = currentValidations[normalizedType];
        const currentLength = state.planDesignIndex.length;
        if (currentLength !== numPlanDesigns) {
          state.planDesignIndex = planDesignIndices.slice();
          const newInitialized = Array(numPlanDesigns).fill(false);
          const newSuccess = Array(numPlanDesigns).fill(false);
          const newWarning = Array(numPlanDesigns).fill(false);
          for (let i = 0; i < numPlanDesigns; i++) {
            newInitialized[i] =
              i < currentLength ? state.initialized[i] : false;
            // Always default to false for success/warning on resize
            newSuccess[i] = false;
            newWarning[i] = false;
          }
          state.initialized = newInitialized;
          state.success = newSuccess;
          state.warning = newWarning;
          updated = true;
        }
      }
    });
    // Remove product types no longer present from root
    Object.keys(currentValidations).forEach((key) => {
      // Remove if not a number and not in allProductTypes
      if (isNaN(Number(key)) && !allProductTypes.has(key)) {
        delete currentValidations[key];
        updated = true;
      }
    });

    const prevValidations = formMethods.getValues('validations');
    const nextValidations = { ...currentValidations };
    if (
      updated &&
      JSON.stringify(prevValidations) !== JSON.stringify(nextValidations)
    ) {
      formMethods.setValue('validations', nextValidations, {
        shouldValidate: false,
        shouldDirty: false,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formMethods, formMethods.watch('plan.plan_designs')]);

  // Optionally, return the validations state for convenience
  return formMethods.watch('validations');
}

// --- NEW HOOK: useSyncValidationStateWithApi ---

/**
 * Syncs the validations state with the latest validationData from the API.
 * For pages 1-11: initialized = true if present in validationData, success = !present if initialized.
 * For pages 12+: initialized[index] = true if any error/warning for that plan design, success[index] = true if initialized and no error/warning for that plan design.
 *
 * If autoSaveHandler is provided, it will be called after validations are updated.
 */
export function useSyncValidationStateWithApi(
  formMethods: UseFormReturn<any>,
  validationData: any, // Should be ValidationResponse | null
  autoSaveHandler?: (values: any) => Promise<any>
) {
  useEffect(() => {
    const results =
      validationData && validationData.results ? validationData.results : {};
    const currentValidations = formMethods.getValues('validations') || {};
    let updated = false;

    // Process each page's validation results
    Object.entries(currentValidations).forEach(([key, pageStateRaw]) => {
      const pageKey = String(key);
      const pageNum = Number(key);
      const pageResult = results[pageKey] ?? {
        errors: [],
        warnings: [],
        validation_results: { errors: [], warnings: [] },
      };

      if (SIMPLE_PAGES.includes(pageNum)) {
        // Handle simple pages (1-11)
        const pageState = pageStateRaw as PageValidationStateSimple;
        if (pageState && typeof pageState === 'object') {
          // Only process if this page has results in the validation data
          const hasPageResults = results[pageKey] !== undefined;

          if (hasPageResults) {
            // Set initialized to true since we have validation results for this page
            if (!pageState.initialized) {
              pageState.initialized = true;
              updated = true;
            }

            // Update success and warning based on actual errors/warnings
            const hasErrors =
              (pageResult.errors?.length ?? 0) > 0 ||
              (pageResult.validation_results?.errors?.length ?? 0) > 0;
            const hasWarnings =
              (pageResult.warnings?.length ?? 0) > 0 ||
              (pageResult.validation_results?.warnings?.length ?? 0) > 0;

            if (pageState.success !== !hasErrors) {
              pageState.success = !hasErrors;
              updated = true;
            }
            if (pageState.warning !== hasWarnings) {
              pageState.warning = hasWarnings;
              updated = true;
            }
          }
        }
      } else {
        // Handle array pages (12-23)
        const pageState = pageStateRaw as PageValidationStateWithArray;
        if (
          pageState &&
          typeof pageState === 'object' &&
          Array.isArray(pageState.initialized)
        ) {
          // Only process if this page has results in the validation data
          const hasPageResults = results[pageKey] !== undefined;

          if (hasPageResults) {
            // Collect error and warning indexes specifically for this page
            const errorIndexes = new Set<number>();
            const warningIndexes = new Set<number>();

            const collectIndexes = (arr: any[], targetSet: Set<number>) => {
              arr.forEach((item) => {
                if (item && typeof item.field === 'string') {
                  const match = item.field.match(/^plan_designs\.(\d+)\./);
                  if (match) {
                    targetSet.add(Number(match[1]));
                  }
                }
              });
            };

            // Collect from both errors/warnings and validation_results
            collectIndexes(pageResult.errors || [], errorIndexes);
            collectIndexes(pageResult.warnings || [], warningIndexes);
            if (pageResult.validation_results) {
              collectIndexes(
                pageResult.validation_results.errors || [],
                errorIndexes
              );
              collectIndexes(
                pageResult.validation_results.warnings || [],
                warningIndexes
              );
            }

            // Update initialized, success, and warning for each plan design
            for (let i = 0; i < pageState.initialized.length; i++) {
              // Only set initialized to true if this plan design has validation data
              const hasValidationData =
                errorIndexes.has(i) || warningIndexes.has(i);

              if (hasValidationData && !pageState.initialized[i]) {
                pageState.initialized[i] = true;
                updated = true;
              }

              // Only update success/warning if initialized
              if (pageState.initialized[i]) {
                const hasErr = errorIndexes.has(i);
                const hasWarn = warningIndexes.has(i);

                // If initialized but no validation data for this index on this page,
                // that means validation passed (no errors/warnings)
                const actualHasErr = hasValidationData ? hasErr : false;
                const actualHasWarn = hasValidationData ? hasWarn : false;

                if (pageState.success[i] !== !actualHasErr) {
                  pageState.success[i] = !actualHasErr;
                  updated = true;
                }
                if (pageState.warning[i] !== actualHasWarn) {
                  pageState.warning[i] = actualHasWarn;
                  updated = true;
                }
              }
            }
          }
        }
      }
    });

    // --- PRODUCT TYPE VALIDATION STATE SYNC ---
    const productTypeResults = results['24'];
    if (productTypeResults) {
      const productTypeErrors = productTypeResults.errors ?? [];
      const productTypeWarnings = productTypeResults.warnings ?? [];
      const planDesigns = formMethods.getValues('plan.plan_designs') || [];

      const productTypes = Object.keys(currentValidations).filter((key) =>
        isNaN(Number(key))
      );

      productTypes.forEach((pt: string) => {
        const state = currentValidations[pt];
        if (state && Array.isArray(state.initialized)) {
          for (let i = 0; i < planDesigns.length; i++) {
            // Find errors/warnings for this productType and planDesignIndex
            const hasErr = productTypeErrors.some((item: any) => {
              const match = item.field?.match(
                /^plan_designs\.(\d+)\.plan_design_ancillaries\.(\d+)\./
              );
              if (!match) return false;
              const planIdx = Number(match[1]);
              const ancillaryIdx = Number(match[2]);
              const ancillary =
                planDesigns[planIdx]?.plan_design_ancillaries?.[ancillaryIdx];
              return (
                planIdx === i &&
                toKebabCase(ancillary?.product_type || '') === pt
              );
            });
            const hasWarn = productTypeWarnings.some((item: any) => {
              const match = item.field?.match(
                /^plan_designs\.(\d+)\.plan_design_ancillaries\.(\d+)\./
              );
              if (!match) return false;
              const planIdx = Number(match[1]);
              const ancillaryIdx = Number(match[2]);
              const ancillary =
                planDesigns[planIdx]?.plan_design_ancillaries?.[ancillaryIdx];
              return (
                planIdx === i &&
                toKebabCase(ancillary?.product_type || '') === pt
              );
            });

            // Set initialized if there's validation data
            if ((hasErr || hasWarn) && !state.initialized[i]) {
              state.initialized[i] = true;
              updated = true;
            }

            // Only set success/warning if initialized is true
            if (state.initialized[i]) {
              if (state.success[i] !== !hasErr) {
                state.success[i] = !hasErr;
                updated = true;
              }
              if (state.warning[i] !== hasWarn) {
                state.warning[i] = hasWarn;
                updated = true;
              }
            }
          }
        }
      });
    }

    const prevValidations = formMethods.getValues('validations');
    const nextValidations = { ...currentValidations };
    if (
      updated &&
      JSON.stringify(prevValidations) !== JSON.stringify(nextValidations)
    ) {
      formMethods.setValue('validations', nextValidations, {
        shouldValidate: false,
        shouldDirty: false,
      });
      if (autoSaveHandler) {
        autoSaveHandler(formMethods.getValues());
      }
    }
  }, [
    formMethods,
    validationData,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    formMethods.watch('validations'),
    autoSaveHandler,
  ]);
}
