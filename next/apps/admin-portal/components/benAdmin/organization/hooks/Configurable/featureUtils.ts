import { Feature } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { UseFormReturn } from 'react-hook-form';

import { createFeatureComponent } from '../../components/ChangeRequestIntake/MenuItems/PlanDesignTool/ClinicalDesigns/DynamicForm/featureComponentFactory';
import { extractSuffix, groupFeaturesByPrefixPattern } from './groupFeatures';

export function generateFeatureTabId(feature: Feature): string {
  if (!feature) {
    return 'UNKNOWN_FEATURE_ITEM';
  }
  if (feature.name) {
    return convertToIdFormat(feature.name);
  }
  if (feature.product_feature_id) {
    return `PRODUCT_FEATURE_${feature.product_feature_id}_ITEM`;
  }
  if (feature.product_class_feature_id) {
    return `PRODUCT_CLASS_FEATURE_${feature.product_class_feature_id}_ITEM`;
  }
  return `FEATURE_${feature.product_feature_id || 'UNKNOWN'}_ITEM`;
}

export function generateFeatureGroupKey(feature: Feature): string {
  if (!feature) {
    return 'unknownFeatureGroup';
  }
  if (feature.name) {
    return toCamelCase(feature.name) + 'Group';
  }
  if (feature.product_feature_id) {
    return `productFeature${feature.product_feature_id}Group`;
  }
  if (feature.product_class_feature_id) {
    return `productClassFeature${feature.product_class_feature_id}Group`;
  }
  return `feature${feature.product_feature_id || 'Unknown'}Group`;
}

export function convertToIdFormat(input: string): string {
  return input
    .toUpperCase()
    .replace(/[^A-Z0-9]+/g, '_')
    .replace(/^_+|_+$/g, '')
    .concat('_ITEM');
}

export function toCamelCase(str: string): string {
  return str
    .replace(/[-_\s]+(.)?/g, (_, c) => (c ? c.toUpperCase() : ''))
    .replace(/^([A-Z])/, (m) => m.toLowerCase());
}

export function getFirstFeatureId(features: Feature[]): string | undefined {
  if (!Array.isArray(features) || features.length === 0) {
    return undefined;
  }
  const firstFeature = features[0];
  return generateFeatureTabId(firstFeature);
}

/**
 * Utility function to filter features based on include/exclude patterns
 *
 * @param features The array of features to filter
 * @param includePatterns Optional array of patterns to include (takes precedence)
 * @param excludePatterns Optional array of patterns to exclude
 * @returns Filtered array of features
 */
export function filterFeatures(
  features: Feature[] | undefined,
  includePatterns?: string[],
  excludePatterns?: string[]
): Feature[] {
  // Early return if no features
  if (!features || !Array.isArray(features) || features.length === 0) {
    return [];
  }

  // If no include/exclude patterns, return all features
  if (!includePatterns?.length && !excludePatterns?.length) {
    return features;
  }

  // Apply filtering
  return features.filter((feature) => {
    const featureName = feature.label || feature.name || '';

    // If include patterns are specified, the feature must match at least one
    if (includePatterns?.length) {
      const matchesInclude = includePatterns.some((pattern) =>
        featureName.includes(pattern)
      );

      // If it doesn't match any include pattern, exclude it
      if (!matchesInclude) {
        return false;
      }
    }

    // If exclude patterns are specified, the feature must not match any
    if (excludePatterns?.length) {
      const matchesExclude = excludePatterns.some((pattern) =>
        featureName.includes(pattern)
      );

      // If it matches any exclude pattern, exclude it
      if (matchesExclude) {
        return false;
      }
    }

    // If it passed all filters, include it
    return true;
  });
}

export function buildGroupedSidebarItems(
  features: Feature[],
  formMethods: UseFormReturn<any>,
  navigateFn: (id: string) => void,
  includePatterns?: string[],
  excludePatterns?: string[],
  priorAuthReviewFeature?: Feature
): any[] {
  if (!Array.isArray(features) || features.length === 0) {
    return [];
  }

  // First, get all the feature IDs for navigation
  const allFeatureIds = features.map((feature) =>
    generateFeatureTabId(feature)
  );

  // Group features by common prefix patterns using the shared utility
  const featureGroups = groupFeaturesByPrefixPattern(features);

  // Process each group and build sidebar items
  const sidebarItems: any[] = [];

  Object.entries(featureGroups).forEach(([groupName, groupFeatures]) => {
    // Apply filtering based on include/exclude patterns
    let shouldIncludeGroup = true;

    // If include patterns specified, group must match at least one
    if (includePatterns?.length) {
      shouldIncludeGroup = includePatterns.some((pattern) =>
        groupName.includes(pattern)
      );
    }

    // If exclude patterns specified, group must not match any
    if (shouldIncludeGroup && excludePatterns?.length) {
      shouldIncludeGroup = !excludePatterns.some((pattern) =>
        groupName.includes(pattern)
      );
    }

    // Skip this group if it should be excluded
    if (!shouldIncludeGroup) {
      return;
    }

    // Handle single feature in group
    if (groupFeatures.length === 1) {
      const feature = groupFeatures[0];
      const featureId = generateFeatureTabId(feature);
      const featureIndex = features.findIndex((f) => f === feature);

      // Calculate next and previous item IDs
      const nextItemId =
        featureIndex < features.length - 1
          ? allFeatureIds[featureIndex + 1]
          : undefined;
      const previousItemId =
        featureIndex > 0 ? allFeatureIds[featureIndex - 1] : undefined;

      sidebarItems.push({
        id: featureId,
        label:
          feature.label ||
          feature.name ||
          `Feature ${feature.product_feature_id}`,
        component: createFeatureComponent(
          feature,
          formMethods,
          navigateFn,
          nextItemId,
          previousItemId
        ),
      });
    }
    // Handle multiple features with dropdown
    else if (groupFeatures.length > 1) {
      // Generate a group ID
      const groupId = `${groupName
        .toUpperCase()
        .replace(/[^A-Z0-9]+/g, '_')}_GROUP`;

      // Create dropdown items
      const dropdownItems = groupFeatures.map((feature) => {
        const featureId = generateFeatureTabId(feature);
        const featureIndex = features.findIndex((f) => f === feature);

        // Calculate next and previous item IDs
        const nextItemId =
          featureIndex < features.length - 1
            ? allFeatureIds[featureIndex + 1]
            : undefined;
        const previousItemId =
          featureIndex > 0 ? allFeatureIds[featureIndex - 1] : undefined;

        // Extract suffix using the shared utility function
        let itemLabel = extractSuffix(
          feature.label || feature.name || '',
          groupName
        );

        // If somehow we ended up with an empty label, use the full label
        if (!itemLabel.trim()) {
          itemLabel =
            feature.label ||
            feature.name ||
            `Feature ${feature.product_feature_id}`;
        }

        return {
          id: featureId,
          label: itemLabel,
          component: createFeatureComponent(
            feature,
            formMethods,
            navigateFn,
            nextItemId,
            previousItemId,
            priorAuthReviewFeature
          ),
        };
      });

      // Add the group dropdown
      sidebarItems.push({
        id: groupId,
        label: groupName,
        hasDropdown: true,
        dropdownItems,
      });
    }
  });

  return sidebarItems;
}

export interface PlanFeatureMapping {
  product_feature_id?: number | null;
  product_class_feature_id?: number | null;
}

/**
 * Maps plan features to their corresponding metadata features
 * This logic was duplicated across multiple files and has been consolidated here
 *
 * @param planFeatures Array of plan features from form data
 * @param metadataFeatures Array of metadata features from organization details
 * @param excludeFeatures Optional array of feature names to exclude (e.g., ['Pharmacy Network'])
 * @returns Array of relevant metadata features that match the plan features
 */
export const mapPlanFeaturesToMetadata = <T extends Feature>(
  planFeatures: PlanFeatureMapping[],
  metadataFeatures: T[] | undefined,
  excludeFeatures: string[] = []
): T[] => {
  if (!Array.isArray(planFeatures) || !Array.isArray(metadataFeatures)) {
    return [];
  }

  return planFeatures
    .map((planFeature: PlanFeatureMapping) => {
      return metadataFeatures.find((metaFeature: T) => {
        return (
          (planFeature.product_feature_id &&
            metaFeature.product_feature_id ===
              planFeature.product_feature_id) ||
          (planFeature.product_class_feature_id &&
            metaFeature.product_class_feature_id ===
              planFeature.product_class_feature_id)
        );
      });
    })
    .filter((feature: T | undefined): feature is T => {
      if (!feature) return false;

      // Filter out excluded features if any
      if (excludeFeatures.length > 0) {
        return !excludeFeatures.some((excludeName) =>
          feature.name?.includes(excludeName)
        );
      }

      return true;
    });
};

/**
 * Gets the first relevant metadata feature from plan features
 * Used when you only need the first matching feature (e.g., for navigation defaults)
 *
 * @param planFeatures Array of plan features from form data
 * @param metadataFeatures Array of metadata features from organization details
 * @returns First matching metadata feature or undefined
 */
export const getFirstRelevantFeature = <T extends Feature>(
  planFeatures: PlanFeatureMapping[],
  metadataFeatures: T[] | undefined
): T | undefined => {
  const relevantFeatures = mapPlanFeaturesToMetadata(
    planFeatures,
    metadataFeatures
  );
  return relevantFeatures[0];
};

/**
 * Safely extracts plan features from form data
 * Handles different data structures and provides a consistent interface
 *
 * @param formData The form data object
 * @param path Optional custom path to plan features (defaults to standard path)
 * @returns Array of plan features or empty array if not found
 */
export const extractPlanFeatures = (
  formData: any,
  path = 'plan.plan_designs.0.plan_features'
): PlanFeatureMapping[] => {
  try {
    // Handle nested path access safely
    const pathParts = path.split('.');
    let current = formData;

    for (const part of pathParts) {
      if (part.includes('[') && part.includes(']')) {
        // Handle array access like 'plan_designs[0]'
        const [arrayName, indexStr] = part.split('[');
        const index = parseInt(indexStr.replace(']', ''));
        current = current?.[arrayName]?.[index];
      } else {
        current = current?.[part];
      }

      if (!current) break;
    }

    return Array.isArray(current) ? current : [];
  } catch (error) {
    console.warn('Failed to extract plan features from path:', path, error);
    return [];
  }
};
