import { InlineEditColumn } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/FormTable';
import { useMemo } from 'react';

import {
  formatCurrency,
  formatDate,
  formatPercentage,
  formatYesNo,
} from './utils/costShareUtils';

/**
 * Pure function to generate cost share columns, optionally read-only.
 */
export function getCostShareColumns(
  maps: any,
  isESI: boolean,
  columnWidth = '200px',
  readOnly = false
): InlineEditColumn[] {
  const editable = (def: boolean) => (readOnly ? false : def);
  const baseColumns: InlineEditColumn[] = [
    {
      key: 'name',
      header: 'Cost Share Tier Name',
      editable: false,
      editType: 'text',
      placeholder: 'Set by system...',
    },
    {
      key: 'effective_date',
      header: 'Effective Date',
      editable: editable(true),
      editType: 'date',
      placeholder: 'Select date',
      format: (value: any) => formatDate(value),
    },
    {
      key: 'expiration_date',
      header: 'Expiration Date',
      editable: editable(true),
      editType: 'date',
      placeholder: 'Select date',
      format: (value: any) => formatDate(value),
    },
    {
      key: 'tier_ind',
      header: 'Drug Tier',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.costShareTierMap,
      searchable: true,
      placeholder: 'Select tier...',
      format: (value: any) =>
        value ? `${maps.costShareTierMap?.[value] || value}` : '--',
    },
    {
      key: 'pharmacy_channel_ind',
      header: 'Pharmacy Channel',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.pharmacyChannelMap,
      searchable: true,
      placeholder: 'Select channel...',
      format: (value: any) => maps.pharmacyChannelMap?.[value] || value || '--',
    },
    {
      key: 'days_supply',
      header: 'Days Supply',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.costShareTierDaysSupplyMap,
      placeholder: 'Select days...',
      format: (value: any) =>
        maps.costShareTierDaysSupplyMap?.[value] || value || '--',
    },
    {
      key: 'co_pay_amount',
      header: 'Copay Amount',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter amount...',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'co_insurance_pct',
      header: 'Coinsurance %',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter %...',
      format: (value: any) => formatPercentage(value),
    },
    {
      key: 'co_insurance_ltgt_ind',
      header: 'Lesser/Greater',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.coInsuranceLesserGreaterMap,
      placeholder: 'Select...',
      format: (value: any) =>
        maps.coInsuranceLesserGreaterMap?.[value] || value || '--',
    },
    {
      key: 'co_insurance_min_amount',
      header: 'Min Amount',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Min amount...',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'co_insurance_max_amount',
      header: 'Max Amount',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Max amount...',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'network_status_ind',
      header: 'Network Status',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.networkStatusMap,
      placeholder: 'Select status...',
      format: (value: any) =>
        value ? `${maps.networkStatusMap?.[value] || '--'}` : '--',
    },
    {
      key: 'pre_packaged_ind',
      header: 'Prepackage Indicator',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select...',
      format: (value: any) => maps.yesNoMap?.[value] || value || '--',
    },
    // ESI-only fields
    ...(isESI
      ? [
          {
            key: 'drug_list_ind',
            header: 'Drug List',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiDrugListMap,
            searchable: true,
            placeholder: 'Select list...',
            format: (value: any) =>
              maps.esiDrugListMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_type_ind',
            header: 'Copay Type Indicator',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayTierMap,
            placeholder: 'Select type...',
            format: (value: any) =>
              maps.esiCopayTierMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_channel_ind',
            header: 'Copay Channel Delivery System',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayChannelMap,
            placeholder: 'Select channel...',
            format: (value: any) =>
              maps.esiCopayChannelMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_structure_ind',
            header: 'Copay Structure',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayStructureMap,
            placeholder: 'Select structure...',
            format: (value: any) =>
              maps.esiCopayStructureMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_network_ind',
            header: 'Copay Network',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayNetworkMap,
            placeholder: 'Select network...',
            format: (value: any) =>
              maps.esiCopayNetworkMap?.[value] || value || '--',
          },
        ]
      : []),
    {
      key: 'include_in_pbc',
      header: 'Include PBC',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select...',
      format: (value: any) => {
        return formatYesNo(value);
      },
    },
    {
      key: 'include_in_pdx',
      header: 'Include in PDX',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select...',
      format: (value: any) => {
        return formatYesNo(value);
      },
    },
    {
      key: 'pbc_print_order',
      header: 'PBC Print Order',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter order...',
      format: (value: any) => value || '--',
    },
    {
      key: 'pdx_print_order',
      header: 'PDX Print Order',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter order...',
      format: (value: any) => value || '--',
    },
  ];
  return baseColumns.map((column) => ({ ...column, width: columnWidth }));
}

/**
 * React hook to memoize cost share columns for editable tables.
 */
export function useCostShareColumns(
  maps: any,
  isESI: boolean,
  columnWidth = '200px'
): InlineEditColumn[] {
  return useMemo(
    () => getCostShareColumns(maps, isESI, columnWidth, false),
    [maps, isESI, columnWidth]
  );
}
