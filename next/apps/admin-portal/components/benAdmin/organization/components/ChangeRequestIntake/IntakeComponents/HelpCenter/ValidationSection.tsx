import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Badge,
  Box,
  Flex,
  Heading,
  Icon,
  Text,
  VStack,
} from '@chakra-ui/react';
import {
  ValidationResults,
  ValidationRule,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PiWarningFill, PiWarningOctagonFill } from 'react-icons/pi';

// Group validation rules by rule name and message to avoid duplicates
const groupValidationRules = (fields: ValidationRule[]) => {
  const grouped = new Map<
    string,
    { rule: ValidationRule; count: number; fields: string[] }
  >();

  fields.forEach((field) => {
    const key = `${field.rule_name}|${field.validation_message}`;
    const existing = grouped.get(key);

    if (existing) {
      existing.count += 1;
      if (field.field) {
        existing.fields.push(field.field);
      }
    } else {
      grouped.set(key, {
        rule: field,
        count: 1,
        fields: field.field ? [field.field] : [],
      });
    }
  });

  return Array.from(grouped.values());
};

const ValidatedFieldsAccordion = ({
  type,
  fields,
  onRuleNameClick,
  addonProductValidationSummary,
}: {
  type: string;
  fields: ValidationRule[];
  onRuleNameClick?: (ruleName: string) => void;
  addonProductValidationSummary?: {
    errors: Record<string, Record<string, string[]>>;
    warnings: Record<string, Record<string, string[]>>;
  };
}) => {
  const groupedRules = groupValidationRules(fields);
  const totalCount = fields.length;

  return (
    <Accordion allowMultiple color="#69696A">
      <AccordionItem
        bg={type === 'Error' ? '#FEF0EF' : '#FFF8E8'}
        mb={8}
        boxShadow="md"
        borderTop="4px"
        borderTopColor={type === 'Error' ? '#DF5D53' : '#ED8936'}
        borderRadius="8px"
      >
        <Flex alignItems="center">
          <AccordionButton>
            <Flex align="center" flex="1" textAlign="left">
              {type === 'Error' ? (
                <Icon as={PiWarningOctagonFill} color="#DF5D53" boxSize={6} />
              ) : (
                <Icon as={PiWarningFill} color="#ED8936" boxSize={6} />
              )}

              <Heading fontSize={16} m={4}>
                {type === 'Error' ? 'Errors' : 'Warnings'} on Page
              </Heading>

              <Badge
                colorScheme={type === 'Error' ? 'red' : 'orange'}
                fontSize="xs"
                borderRadius="full"
              >
                {totalCount}
              </Badge>
            </Flex>
            <AccordionIcon />
          </AccordionButton>
        </Flex>
        <AccordionPanel color="#873832" pb={4}>
          <VStack spacing={4} align="stretch">
            {groupedRules.map((group, index) => (
              <Box key={`${group.rule.plan_validation_rule_id}-${index}`}>
                <Flex align="center" gap={2} mb={2}>
                  {onRuleNameClick ? (
                    <Text
                      as="button"
                      onClick={() => onRuleNameClick(group.rule.rule_name)}
                      color="red.600"
                      fontWeight="semibold"
                      fontSize="md"
                      textDecoration="underline"
                      cursor="pointer"
                      _hover={{ color: 'red.800' }}
                      background="none"
                      border="none"
                      p={0}
                    >
                      {group.rule.rule_name}
                    </Text>
                  ) : (
                    <Heading
                      size="sm"
                      color={type === 'Error' ? '#DF5D53' : '#ED8936'}
                    >
                      {group.rule.rule_name}
                    </Heading>
                  )}
                  {group.count > 1 && (
                    <Badge
                      colorScheme={type === 'Error' ? 'red' : 'orange'}
                      size="sm"
                      borderRadius="full"
                    >
                      {group.count} instances
                    </Badge>
                  )}
                </Flex>
                <Text fontSize="sm" color="#666" mb={2}>
                  {group.rule.validation_message}
                </Text>
                {group.fields.length > 0 && group.count > 1 && (
                  <Text fontSize="xs" color="#999">
                    Affects: {group.fields.join(', ')}
                  </Text>
                )}
              </Box>
            ))}
          </VStack>
          {/* Custom grouped summary for add-on products, inside the error accordion */}
          {type === 'Error' &&
            addonProductValidationSummary?.errors &&
            Object.keys(addonProductValidationSummary.errors).length > 0 && (
              <Box mt={1} ml={2}>
                {/* Only show product names and plan design names/messages, not product_type */}
                {Object.values(addonProductValidationSummary.errors).map(
                  (products) =>
                    Object.entries(products).map(([productName, planMsgs]) => (
                      <Box key={productName} mb={3}>
                        <Text
                          fontWeight="bold"
                          color="red.600"
                          fontSize="lg"
                          mb={2}
                        >
                          {productName}
                        </Text>
                        {planMsgs.map((msg, i) => {
                          // Extract plan name from the message
                          const match = msg.match(
                            /^(.*?)'s effective date is null or empty$/
                          );
                          const planName = match ? match[1] : msg;
                          return (
                            <Box key={i} mb={2} ml={4}>
                              <Text
                                fontWeight="semibold"
                                fontSize="md"
                                color="#222"
                                mb={1}
                              >
                                {planName}
                              </Text>
                              <Text fontSize="sm" color="#444" ml={4}>
                                Effective date is null or empty.
                              </Text>
                            </Box>
                          );
                        })}
                      </Box>
                    ))
                )}
              </Box>
            )}
        </AccordionPanel>
      </AccordionItem>
    </Accordion>
  );
};

export const ValidationSection = ({
  validatedFields,
  onRuleNameClick,
  addonProductValidationSummary,
}: {
  validatedFields: ValidationResults;
  onRuleNameClick?: (ruleName: string) => void;
  addonProductValidationSummary?: {
    errors: Record<string, Record<string, string[]>>;
    warnings: Record<string, Record<string, string[]>>;
  };
}) => {
  const errors = validatedFields.errors;
  const warnings = validatedFields.warnings;
  return (
    <>
      {errors?.length > 0 && (
        <ValidatedFieldsAccordion
          type="Error"
          fields={errors}
          onRuleNameClick={onRuleNameClick}
          addonProductValidationSummary={addonProductValidationSummary}
        />
      )}

      {warnings?.length > 0 && (
        <ValidatedFieldsAccordion
          type="Warning"
          fields={warnings}
          onRuleNameClick={onRuleNameClick}
        />
      )}
    </>
  );
};
