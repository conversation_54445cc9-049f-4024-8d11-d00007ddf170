import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

/**
 * useCompoundForm Hook
 *
 * Generates form fields for compound coverage configuration.
 * Handles the relationship between compounds covered status and
 * the ability to select a patient pay tier for compounds.
 *
 * @param currentDetails - Current organization details
 * @param costShareDesignOptions - Available cost share design options
 * @param onCostShareDesignChange - Handler for cost share design selection
 * @param index - Plan design index
 * @param isCompoundSelectDisabled - Whether compound selection is disabled
 * @param onCompoundsCoveredChange - Handler for compounds covered change
 * @returns Form subcategories configuration
 */
export function useCompoundForm(
  currentDetails: Partial<OrganizationDetails>,
  costShareDesignOptions?: Record<string, string>,
  onCostShareDesignChange?: (value: string) => void,
  index = 0,
  isCompoundSelectDisabled = true,
  onCompoundsCoveredChange?: (value: string) => void
) {
  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const { compoundCopayMap, yesNoMap } = usePicklistMaps();

  // Check if there are any cost share design options
  const hasOptions =
    costShareDesignOptions && Object.keys(costShareDesignOptions).length > 0;

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        // Are Compounds Covered dropdown
        defineFormField(
          'Are Compounds Covered',
          'dropdownSelect',
          `${PLAN_DESIGNS_BASE_PATH}.${indexToUse}.plan_design_details.0.compounds_ind`,
          currentDetails?.plan?.plan_designs?.[indexToUse]
            ?.plan_design_details?.[0]?.compounds_ind,
          {
            isRequired: true,
            infoText: !hasOptions
              ? 'No cost share tiers available'
              : 'Select if covered or not',
            optionsMap: yesNoMap,
            onChange: onCompoundsCoveredChange,
          }
        ),
        // Patient Pay Tier Compounds dropdown
        defineFormField(
          'Select Patient Pay Tier Compounds',
          'dropdownSelect',
          `${PLAN_DESIGNS_BASE_PATH}.${indexToUse}.plan_design_details.0.compound_copay_ind`,
          null,
          {
            optionsMap: compoundCopayMap,
            onChange: (value) =>
              onCostShareDesignChange ? onCostShareDesignChange(value) : {},
            isDisabled: isCompoundSelectDisabled,
            infoText: 'Componds must be covered',
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
