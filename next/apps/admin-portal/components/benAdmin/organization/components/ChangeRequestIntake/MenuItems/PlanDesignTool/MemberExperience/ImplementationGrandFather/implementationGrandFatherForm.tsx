import {
  HelpText,
  OrganizationDetails,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useFormFieldsWithHelp } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useFormFieldsWithHelp';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { implementationGrandFatherConfig } from '../../../../../Tabs/PlanDesign/MemberExperience/Config/implementationGrandFatherConfig';

export const useImplementationGrandFatherForm = (
  currentDetails: Partial<OrganizationDetails>,
  helpText?: HelpText
) => {
  const { grandfatherPaTimeframeMap, yesNoMap } = usePicklistMaps();

  const { defineFormFieldWithHelp } = useFormFieldsWithHelp(helpText);

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Grandfather Prior Authorization',
          'dropdownSelect',
          implementationGrandFatherConfig.grandfather_pa_ind,
          currentDetails?.plan?.grandfather_pa_ind,
          {
            optionsMap: yesNoMap,
          }
        ),
        defineFormFieldWithHelp(
          'Grandfather PA Timeframe',
          'dropdownSelect',
          implementationGrandFatherConfig.grandfather_pa_timeframe_ind,
          currentDetails?.plan?.grandfather_pa_timeframe_ind,
          {
            optionsMap: grandfatherPaTimeframeMap,
          }
        ),
      ]),

      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Grandfather PAs bypass High Dollar Claim Review',
          'dropdownSelect',
          implementationGrandFatherConfig.grandfather_pa_bypass_hdcr_ind,
          currentDetails?.plan?.grandfather_pa_bypass_hdcr_ind,
          {
            optionsMap: yesNoMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Grandfather PA Note',
          'textarea',
          implementationGrandFatherConfig.grandfather_pa_note,
          currentDetails?.plan?.grandfather_pa_note,
          {
            placeholder: 'Enter your notes',
            infoText: 'Please enter Grandfather PA Notes',
            validations: z
              .string()
              .max(2000, 'Value cannot exceed 2000 characters'),
            rows: 5,
            customProps: {
              minHeight: '120px',
              overflow: 'hidden',
            },
          }
        ),
      ]),

      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Other Grandfathering',
          'textarea',
          implementationGrandFatherConfig.grandfather_other_note,
          currentDetails?.plan?.grandfather_other_note,
          {
            placeholder: 'Enter your notes',
            infoText: 'Please enter Other Grandfathering Notes',
            validations: z
              .string()
              .max(2000, 'Value cannot exceed 2000 characters'),
            rows: 5,
            customProps: {
              minHeight: '120px',
              overflow: 'hidden',
            },
          }
        ),
      ]),
    ]),
  ];

  return { subCategories };
};
