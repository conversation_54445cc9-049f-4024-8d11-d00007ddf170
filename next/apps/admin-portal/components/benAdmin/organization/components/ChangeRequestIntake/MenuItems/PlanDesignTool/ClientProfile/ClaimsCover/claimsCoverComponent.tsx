import { Flex } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
import { useValidationErrorsForForm } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useValidationErrorsForForm';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  CLAIMS_COVER_ITEM,
  ELIGIBILITY_ITEM,
  FSA_HRA_HSA_ITEM,
} from '../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../Navigation/uiContextEnum';
import { useClaimsCoverForm } from './claimsCoverForm';
interface ClaimsCoverComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const ClaimsCoverComponent: React.FC<ClaimsCoverComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(CLAIMS_COVER_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(CLAIMS_COVER_ITEM);

  const uiContextIndex = getUIContextFromNavigationConstant(CLAIMS_COVER_ITEM);

  //   const submitHandler = useSaveChangeRequestHandler(formMethods);

  //   const handleSaveAndExit = () => {
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  const { subCategories } = useClaimsCoverForm(
    currentDetails as Partial<OrganizationDetails>
  );

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  const handleContinue = createContinueHandler(
    CLAIMS_COVER_ITEM,
    FSA_HRA_HSA_ITEM
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(ELIGIBILITY_ITEM);
    }
  };

  const contextIndex = getUIContextFromNavigationConstant(CLAIMS_COVER_ITEM);

  useValidationErrorsForForm({
    subCategories,
    formMethods,
    validationData,
    contextIndex,
  });
  return (
    <Flex justify="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="Claims Cover"
        formDescription=""
        subCategories={subCategories}
        onContinue={handleContinue}
        onBack={handleBack}
        // onSaveExit={handleSaveAndExit}
        isProcessing={isLoading}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default ClaimsCoverComponent;
