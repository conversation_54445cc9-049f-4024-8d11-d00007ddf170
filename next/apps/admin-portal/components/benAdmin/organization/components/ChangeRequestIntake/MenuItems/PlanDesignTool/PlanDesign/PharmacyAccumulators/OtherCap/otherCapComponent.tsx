import { Box, Flex } from '@chakra-ui/react';
import { getAccumsGeneralPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/accumsGeneralConfig';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  MAXIMUM_OUT_OF_POCKET_ITEM,
  OTHER_CAP_ITEM,
  PLAN_DESIGN_REVIEW_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../../Navigation/uiContextEnum';
import { useValidateByPageFromContext } from '../../../../../Validations/ValidationContext';
import { OtherCapForm } from './otherCapForm';

const OtherCapComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  //   const submitHandler = useSaveChangeRequestHandler(formMethods);
  const urlIndex = getIndexFromURL() || 0;

  const { refetch, validationData } =
    useValidateByPageFromContext(OTHER_CAP_ITEM);

  const uiContextIndex = getUIContextFromNavigationConstant(OTHER_CAP_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(
    OTHER_CAP_ITEM,
    'plan_design',
    urlIndex
  );

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  // Create the continue handler for this specific page
  const handleContinue = createContinueHandler(
    OTHER_CAP_ITEM,
    PLAN_DESIGN_REVIEW_ITEM
  );

  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  const handleNavigation = (destination: string) => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(destination);
    }
  };

  // need to pass this in to conditionally render fields based on product
  const productName = formMethods.watch('plan.product.name');

  return (
    <Flex>
      <Box flex="3" pr={8}>
        <DynamicCollectionSection
          basePath={getAccumsGeneralPaths(urlIndex).accumulation_other}
          formMethods={formMethods}
          generalForm={<OtherCapForm productName={productName} />}
          title="Pharmacy Accumulators - Other Cap"
          description=""
          emptyMessage="You haven't added anything."
          skipMessage="If this Plan does not have any Other Cap accumulators, skip to the next step."
          isOptional={true}
          modalTitle="Pharmacy Accumulators - Other Cap"
          onBack={() => handleNavigation(MAXIMUM_OUT_OF_POCKET_ITEM)}
          onContinue={handleContinue}
          //   onSaveExit={handleSaveAndExit}
        />
      </Box>
      <Box flex="1">
        <HelpCenter
          helpContent={helpCenterData}
          isLoading={isFetching}
          validationResults={
            uiContextIndex
              ? validationData?.results?.[uiContextIndex]?.validation_results
              : undefined
          }
        />
      </Box>
    </Flex>
  );
};

export default OtherCapComponent;
