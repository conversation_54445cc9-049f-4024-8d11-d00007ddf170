import { Flex } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
import { useValidationErrorsForForm } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useValidationErrorsForForm';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  CLIENT_INFORMATION_ITEM,
  IMPLEMENTATION_ITEM,
  PHARMACY_BENEFITS_MANAGER_ITEM,
} from '../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../Navigation/uiContextEnum';
import { usePharmacyBenefitsManager } from './pharmacyBenefitsManagerForm';

const PharmacyBenefitsManagerComponent: React.FC<{
  formMethods: UseFormReturn<OrganizationDetails>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { refetch, validationData, isLoading } = useValidateByPageFromContext(
    PHARMACY_BENEFITS_MANAGER_ITEM
  );

  const { helpCenterData, isFetching } = useHelpCenter(
    PHARMACY_BENEFITS_MANAGER_ITEM
  );

  const uiContextIndex = getUIContextFromNavigationConstant(
    PHARMACY_BENEFITS_MANAGER_ITEM
  );

  const { subCategories } = usePharmacyBenefitsManager(
    currentDetails as Partial<OrganizationDetails>
  );

  //   const submitHandler = useSaveChangeRequestHandler(formMethods);

  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  const handleContinue = createContinueHandler(
    PHARMACY_BENEFITS_MANAGER_ITEM,
    IMPLEMENTATION_ITEM
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(CLIENT_INFORMATION_ITEM);
    }
  };

  const contextIndex = getUIContextFromNavigationConstant(
    PHARMACY_BENEFITS_MANAGER_ITEM
  );

  useValidationErrorsForForm({
    subCategories,
    formMethods,
    validationData,
    contextIndex,
  });

  return (
    <Flex justify="space-between">
      <GenericForm
        formDescription=""
        formName="Pharmacy Benefits Manager"
        formMethods={formMethods}
        subCategories={subCategories}
        onContinue={handleContinue}
        onBack={handleBack}
        // onSaveExit={handleSaveAndExit}
        isProcessing={isLoading}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default PharmacyBenefitsManagerComponent;
