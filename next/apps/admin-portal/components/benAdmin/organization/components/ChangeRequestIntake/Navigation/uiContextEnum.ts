import {
  ACCUMULATORS_GENERAL_ITEM,
  ADD_ON_PRODUCTS_ITEM,
  CLAIMS_COVER_ITEM,
  CLIENT_INFORMATION_ITEM,
  COMPOUND_ITEM,
  DEDUCTIBLE_ITEM,
  <PERSON><PERSON>P<PERSON><PERSON>_ITEM,
  ELIGIB<PERSON><PERSON><PERSON>_ITEM,
  FSA_HRA_HSA_ITEM,
  GENERAL_ITEM,
  ID_CARDS_ITEM,
  IMPLEMENTATION_GRANDFATHER_ITEM,
  IMPLEMENTATION_ITEM,
  IN_HOUSE_PHARMACY_ITEM,
  MAXIMUM_OUT_OF_POCKET_ITEM,
  MEMBER_SERVICES_ITEM,
  OTHER_CAP_ITEM,
  PHARMACY_BENEFITS_MANAGER_ITEM,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_NETWORK_ITEM,
  PRODUCT_SET_UP_ITEM,
  STANDARD_ITEM,
  TRANSITION_FILES_AND_DETAILS_ITEM,
  UNBREAKABLE_ITEM,
  WELCOME_KIT_AND_LETTERS_ITEM,
  XML_ITEM,
} from './navigationConstants';

// Add the ui context ind of the pages that do not use plan design objects here
export const SIMPLE_PAGES = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 22, 25];

/**
 * Enum mapping ui_context_ind numbers to screen identifiers
 */
export enum UIContextScreen {
  // Client Profile
  CLIENT_INFORMATION = 1,
  PHARMACY_BENEFITS_MANAGER = 2,
  IN_HOUSE_PHARMACY = 3,
  IMPLEMENTATION = 4,
  ELIGIBILITY = 5,
  CLAIMS_COVERAGE = 6,
  FSA_HRA_HSA = 7,

  // Member Experience
  ID_CARDS = 8,
  TRANSITION_FILES_AND_DETAILS = 9,
  WELCOME_KIT_AND_LETTERS = 10,
  MEMBER_SERVICE_PROVIDERS = 11,

  // Plan Design
  GENERAL = 12,
  XML_LEAVE_BEHIND = 13,
  PATIENT_PAY_STANDARD = 14,
  PATIENT_PAY_UNBREAKABLE = 15,
  PATIENT_PAY_COMPOUNDS = 16,
  PATIENT_PAY_DISPENSE_AS_WRITTEN = 17,
  PHARMACY_ACCUMULATORS_GENERAL = 18,
  PHARMACY_ACCUMULATORS_DEDUCTIBLE = 19,
  PHARMACY_ACCUMULATORS_MAXIMUM_OUT_OF_POCKET = 20,
  PHARMACY_ACCUMULATORS_OTHER_CAP = 21,

  // Products and Services
  CORE_PRODUCTS_PRODUCT_SET_UP = 22,
  CORE_PRODUCTS_PHARMACY_NETWORK = 23,
  ADD_ON_PRODUCTS = 24,
  IMPLEMENTATION_GRANDFATHER = 25,
}

/**
 * Mapping from ui_context_ind to navigation constant identifiers
 */
export const UI_CONTEXT_TO_NAVIGATION_MAP: Record<UIContextScreen, string> = {
  [UIContextScreen.CLIENT_INFORMATION]: CLIENT_INFORMATION_ITEM,
  [UIContextScreen.PHARMACY_BENEFITS_MANAGER]: PHARMACY_BENEFITS_MANAGER_ITEM,
  [UIContextScreen.IN_HOUSE_PHARMACY]: IN_HOUSE_PHARMACY_ITEM,
  [UIContextScreen.IMPLEMENTATION]: IMPLEMENTATION_ITEM,
  [UIContextScreen.ELIGIBILITY]: ELIGIBILITY_ITEM,
  [UIContextScreen.CLAIMS_COVERAGE]: CLAIMS_COVER_ITEM,
  [UIContextScreen.FSA_HRA_HSA]: FSA_HRA_HSA_ITEM,
  [UIContextScreen.ID_CARDS]: ID_CARDS_ITEM,
  [UIContextScreen.TRANSITION_FILES_AND_DETAILS]:
    TRANSITION_FILES_AND_DETAILS_ITEM,
  [UIContextScreen.WELCOME_KIT_AND_LETTERS]: WELCOME_KIT_AND_LETTERS_ITEM,
  [UIContextScreen.MEMBER_SERVICE_PROVIDERS]: MEMBER_SERVICES_ITEM,
  [UIContextScreen.GENERAL]: GENERAL_ITEM,
  [UIContextScreen.XML_LEAVE_BEHIND]: XML_ITEM,
  [UIContextScreen.PATIENT_PAY_STANDARD]: STANDARD_ITEM,
  [UIContextScreen.PATIENT_PAY_UNBREAKABLE]: UNBREAKABLE_ITEM,
  [UIContextScreen.PATIENT_PAY_COMPOUNDS]: COMPOUND_ITEM,
  [UIContextScreen.PATIENT_PAY_DISPENSE_AS_WRITTEN]: DISPENSE_ITEM,
  [UIContextScreen.PHARMACY_ACCUMULATORS_GENERAL]: ACCUMULATORS_GENERAL_ITEM,
  [UIContextScreen.PHARMACY_ACCUMULATORS_DEDUCTIBLE]: DEDUCTIBLE_ITEM,
  [UIContextScreen.PHARMACY_ACCUMULATORS_MAXIMUM_OUT_OF_POCKET]:
    MAXIMUM_OUT_OF_POCKET_ITEM,
  [UIContextScreen.PHARMACY_ACCUMULATORS_OTHER_CAP]: OTHER_CAP_ITEM,
  [UIContextScreen.CORE_PRODUCTS_PRODUCT_SET_UP]: PRODUCT_SET_UP_ITEM,
  [UIContextScreen.CORE_PRODUCTS_PHARMACY_NETWORK]: PHARMACY_NETWORK_ITEM,
  [UIContextScreen.ADD_ON_PRODUCTS]: ADD_ON_PRODUCTS_ITEM,
  [UIContextScreen.IMPLEMENTATION_GRANDFATHER]: IMPLEMENTATION_GRANDFATHER_ITEM,
};

/**
 * Reverse mapping from navigation constant to ui_context_ind
 */
export const NAVIGATION_TO_UI_CONTEXT_MAP: Record<string, UIContextScreen> =
  Object.entries(UI_CONTEXT_TO_NAVIGATION_MAP).reduce((acc, [key, value]) => {
    acc[value] = parseInt(key) as UIContextScreen;
    return acc;
  }, {} as Record<string, UIContextScreen>);

/**
 * Helper function to get navigation constant from ui_context_ind
 */
export const getNavigationConstantFromUIContext = (
  uiContextInd: UIContextScreen
): string => {
  return UI_CONTEXT_TO_NAVIGATION_MAP[uiContextInd];
};

/**
 * Helper function to get ui_context_ind from navigation constant
 */
export const getUIContextFromNavigationConstant = (
  navigationConstant: string
): UIContextScreen | undefined => {
  return NAVIGATION_TO_UI_CONTEXT_MAP[navigationConstant];
};
