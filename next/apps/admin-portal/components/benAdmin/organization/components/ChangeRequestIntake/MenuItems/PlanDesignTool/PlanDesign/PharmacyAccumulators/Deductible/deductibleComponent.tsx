import { Box, Flex } from '@chakra-ui/react';
import { getAccumsGeneralPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/accumsGeneralConfig';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  ACCUMULATORS_GENERAL_ITEM,
  DEDUCTIBLE_ITEM,
  MAXIMUM_OUT_OF_POCKET_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../../Navigation/uiContextEnum';
import { useValidateByPageFromContext } from '../../../../../Validations/ValidationContext';
import { DeductibleFormModal } from './deductibleForm';

const DeductibleComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  //   const submitHandler = useSaveChangeRequestHandler(formMethods);
  const urlIndex = getIndexFromURL() || 0;

  const { refetch, validationData } =
    useValidateByPageFromContext(DEDUCTIBLE_ITEM);

  const uiContextIndex = getUIContextFromNavigationConstant(DEDUCTIBLE_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(
    DEDUCTIBLE_ITEM,
    'plan_design',
    urlIndex
  );

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  // Create the continue handler for this specific page
  const handleContinue = createContinueHandler(
    DEDUCTIBLE_ITEM,
    MAXIMUM_OUT_OF_POCKET_ITEM
  );

  //   const handleSaveAndExit = () => {
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  const handleNavigation = (destination: string) => {
    onUpdateActiveItem?.(destination);
  };

  const productName = formMethods.watch('plan.product.name');

  return (
    <Flex>
      <Box flex="3" pr={8}>
        <DynamicCollectionSection
          basePath={getAccumsGeneralPaths(urlIndex).accumulation_deductible}
          formMethods={formMethods}
          generalForm={<DeductibleFormModal productName={productName} />}
          title="Pharmacy Accumulators - Deductible"
          description=""
          emptyMessage="You haven't added anything."
          skipMessage="If this Plan does not have any Deductible accumulators, skip to the next step."
          isOptional={true}
          modalTitle="Pharmacy Accumulators - Deductible"
          onBack={() => handleNavigation(ACCUMULATORS_GENERAL_ITEM)}
          onContinue={handleContinue}
          //   onSaveExit={handleSaveAndExit}
        />
      </Box>
      <Box flex="1">
        <HelpCenter
          helpContent={helpCenterData}
          isLoading={isFetching}
          validationResults={
            uiContextIndex
              ? validationData?.results?.[uiContextIndex]?.validation_results
              : undefined
          }
        />
      </Box>
    </Flex>
  );
};

export default DeductibleComponent;
