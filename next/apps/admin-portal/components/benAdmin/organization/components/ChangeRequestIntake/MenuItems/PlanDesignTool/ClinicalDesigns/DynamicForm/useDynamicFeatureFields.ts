import {
  PLAN_DESIGNS_BASE_PATH,
  SubCategoryType,
} from 'apps/admin-portal/components/benAdmin';
import {
  Feature,
  OrganizationDetails,
  Plan,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  buildPicklistsMap,
  getOptionsMap,
  mapFieldType,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/Configurable/fieldUtils';
import {
  buildFieldPath,
  findMatchingPlanFeatureItem,
  findMatchingPlanFeatures,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planNavigation';
import {
  defineDropdownField,
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { useURLIndex } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/useUrlParameters';
import { useEffect, useMemo, useRef } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { getIdMap, syncAcrossPlanDesigns } from './paramsUtils';

const defaultYesESIConditionSpecificManagementFields = [
  'Cardiovascular Care',
  'Oncology Care',
  'HIV Care Value Program',
  'Multiple Sclerosis Care Value',
  'Rare Conditions Care Value',
  'React with Market Event',
];

/**
 * Hook to dynamically generate form fields for a feature
 * Uses URL parameter detection and optimized field generation
 *
 * @param feature The feature object to generate fields for
 * @param currentDetails The current organization details
 * @returns An object containing the generated subCategories
 */
export function useDynamicFeatureFields(
  feature: Feature,
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<any>,
  priorAuthReviewFeature?: Feature
) {
  // Get the index from URL with polling - this is now provided by our reusable hook
  const urlIndex = useURLIndex(undefined, { debug: false });
  const esiConditionSpecificManagementFeature =
    'ESI-ConditionSpecificManagement';

  const priorAuthFeatureSuffix = 'UtilizationManagementPriorAuthorization';
  const featureNameSuffix = feature.name.split('-')?.[1];
  const priorAuthFeatureName =
    featureNameSuffix === priorAuthFeatureSuffix && feature.name; // this will be in format of [PBM]-UtilizationManagementPriorAuthorization

  const isEligibleForFeatureMerge = feature.name === priorAuthFeatureName;
  let priorAuthReviewerSubCategory: SubCategoryType<any>[] = [];

  const initialCurrentPhoneNumber = useRef<string | null>(null);

  const priorAuthReviewerFeature =
    'UtilizationManagementPriorAuthorizationReviewer';

  // Use memoization to prevent regenerating fields unnecessarily
  const subCategories = useMemo(() => {
    // Early return if feature is invalid
    if (!feature || !feature.feature_items) {
      return [];
    }

    // Get picklists map for quick lookup
    const picklistsMap = buildPicklistsMap(currentDetails.picklists || []);
    const generateSubCategory = (clinicalFeature: Feature) => {
      // Find the matching plan features
      const matchingPlanFeatures = findMatchingPlanFeatures(
        currentDetails.plan as Plan,
        clinicalFeature,
        urlIndex || 0
      );

      // Create fields for all feature items
      const nonTextareaFields: ReturnType<typeof defineFormField>[] = [];
      const textareaFields: ReturnType<typeof defineFormField>[] = [];
      clinicalFeature.feature_items.forEach((item) => {
        // Find the matching plan feature item
        const matchingItem = findMatchingPlanFeatureItem(
          matchingPlanFeatures,
          item
        );

        // Determine field type based on field_type_label
        const fieldType = mapFieldType(item.field_type_label) as any;

        // Get options map for selection fields
        const optionsMap = getOptionsMap(picklistsMap, item);

        // Build the field path with explicit URL index
        const fieldPath = buildFieldPath(matchingItem, urlIndex);

        const idMap = getIdMap(fieldPath, formMethods);

        // Get the current value from the plan structure
        const currentValue = matchingItem ? matchingItem.value : '';

        if (
          clinicalFeature.name === esiConditionSpecificManagementFeature &&
          defaultYesESIConditionSpecificManagementFields.includes(
            item.name || item.label
          )
        ) {
          const currentDefaultValue = formMethods?.getValues(fieldPath);
          if (!currentDefaultValue) {
            formMethods?.setValue(fieldPath, '1');
          }
        }

        let fieldConfig;

        // For dropdown fields, handle toggle features
        if (fieldType === 'dropdownSelect' && optionsMap) {
          // Create a toggleName based on field path for the checkbox state
          const toggleName = `${fieldPath}_differs`;

          // Use defineDropdownField for enhanced dropdown functionality
          fieldConfig = defineDropdownField(
            item.label || '',
            fieldPath,
            currentValue,
            optionsMap,
            {
              infoText: '',
              placeholder: `Select ${item.label}`,
              isRequired: false,
              showToggle: true,
              toggleLabel: 'Differs from Plan Design',
              toggleName: toggleName,
              toggleDefaultValue: false,
              formPath: PLAN_DESIGNS_BASE_PATH,
              sync: true,
              idMap,
              syncFunction: syncAcrossPlanDesigns,
            }
          );
        } else {
          // For other field types, use the standard defineFormField
          fieldConfig = defineFormField(
            item.label || '',
            fieldType,
            fieldPath,
            currentValue,
            {
              infoText: '',
              placeholder: `Enter ${item.label}`,
              isRequired: false,
              ...(optionsMap &&
              ['checkboxGroup', 'radioGroup', 'dropdownSelect'].includes(
                fieldType
              )
                ? { optionsMap }
                : {}),
              // Add toggle and sync properties for input fields
              ...(fieldType === 'input'
                ? {
                    showToggle: true,
                    toggleLabel: 'Differs from Plan Design',
                    toggleName: `${fieldPath}_differs`,
                    toggleDefaultValue: false,
                    formPath: PLAN_DESIGNS_BASE_PATH,
                    sync: true,
                    idMap,
                    syncFunction: syncAcrossPlanDesigns,
                  }
                : {}),
              ...(fieldType === 'textarea'
                ? {
                    showToggle: true,
                    toggleLabel: 'Differs from Plan Design',
                    toggleName: `${fieldPath}_differs`,
                    toggleDefaultValue: false,
                    formPath: PLAN_DESIGNS_BASE_PATH,
                    rows: 5,
                    sync: true,
                    idMap,
                    syncFunction: syncAcrossPlanDesigns,
                    customProps: {
                      minHeight: '120px',
                      resize: 'none',
                      overflow: 'hidden',
                      style: {
                        minHeight: '120px',
                      },
                    },
                  }
                : {}),
            }
          );
        }

        if (fieldType === 'textarea') {
          textareaFields.push(fieldConfig);
        } else {
          nonTextareaFields.push(fieldConfig);
        }
      });

      // Group regular fields into inline groups of 2
      const inlineGroups = [];
      for (let i = 0; i < nonTextareaFields.length; i += 2) {
        inlineGroups.push(
          defineInlineFieldGroup(nonTextareaFields.slice(i, i + 2))
        );
      }

      // Add each textarea field in its own inline group
      textareaFields.forEach((textareaField) => {
        inlineGroups.push(defineInlineFieldGroup([textareaField]));
      });

      // Create a single subcategory with all fields
      return [
        defineSubCategory(
          clinicalFeature.label || clinicalFeature.name || '',
          '',
          inlineGroups
        ),
      ];
    };

    const clinicalFeatureSubCategory = generateSubCategory(feature);
    if (isEligibleForFeatureMerge && priorAuthReviewFeature) {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      priorAuthReviewerSubCategory = generateSubCategory(
        priorAuthReviewFeature
      );
    }
    return [...priorAuthReviewerSubCategory, ...clinicalFeatureSubCategory];
  }, [
    feature,
    currentDetails.picklists,
    currentDetails.plan,
    urlIndex,
    formMethods,
  ]);

  useEffect(() => {
    if (priorAuthReviewFeature) {
      if (
        priorAuthReviewFeature.name !== priorAuthReviewerFeature ||
        !formMethods
      ) {
        return;
      }

      const reviewerItem = priorAuthReviewFeature.feature_items.find(
        (item) => item.name === 'Reviewer'
      );
      const phoneItem = priorAuthReviewFeature.feature_items.find(
        (item) => item.name === 'Phone Number'
      );

      if (reviewerItem && phoneItem) {
        const matchingPlanFeatures = findMatchingPlanFeatures(
          currentDetails.plan as Plan,
          priorAuthReviewFeature,
          urlIndex || 0
        );
        const reviewerFieldPath = buildFieldPath(
          findMatchingPlanFeatureItem(matchingPlanFeatures, reviewerItem),
          urlIndex
        );
        const phoneFieldPath = buildFieldPath(
          findMatchingPlanFeatureItem(matchingPlanFeatures, phoneItem),
          urlIndex
        );

        initialCurrentPhoneNumber.current =
          !initialCurrentPhoneNumber.current &&
          formMethods?.getValues(phoneFieldPath);

        const defaults: { [key: string]: string } = {
          RxBenefits: '**************',
          Caremark: '**************',
          'Express Scripts': '**************',
          OptumRx: '**************',
        };

        // Update phone number when reviewer value changes
        const updatePhoneNumber = () => {
          if (!formMethods) return; // Safety check
          const reviewerValue = formMethods.getValues(reviewerFieldPath);
          if (!initialCurrentPhoneNumber.current)
            formMethods.setValue(phoneFieldPath, defaults[reviewerValue] || '');
        };

        // Initial update
        updatePhoneNumber();

        // Add event listener for form value changes
        const handleFormChange = () => updatePhoneNumber();

        // having a watch on prior auth reviewer field value
        formMethods?.register(reviewerFieldPath, {
          onChange: handleFormChange,
          shouldUnregister: true,
        });
      }
    }
  }, [
    feature,
    currentDetails.plan,
    formMethods,
    priorAuthReviewFeature,
    urlIndex,
  ]);

  return { subCategories };
}
