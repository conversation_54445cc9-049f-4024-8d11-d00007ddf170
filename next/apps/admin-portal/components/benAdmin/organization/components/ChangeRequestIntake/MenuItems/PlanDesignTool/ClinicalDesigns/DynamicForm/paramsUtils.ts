import {
  PLAN_DESIGNS_BASE_PATH,
  PlanDesign,
  PlanFeature,
  PlanFeatureItem,
} from 'apps/admin-portal/components/benAdmin';
import { UseFormReturn } from 'react-hook-form';

interface FeatureIndices {
  featureIndex: number;
  itemIndex: number;
}

interface IdMap {
  key: string;
  value: number;
}

// Get all plan design indices
const getPlanDesignIndices = (formMethods: any): number[] => {
  const planDesigns = formMethods.getValues(PLAN_DESIGNS_BASE_PATH);
  if (!Array.isArray(planDesigns)) return [];
  return Array.from({ length: planDesigns.length }, (_, i) => i);
};

// Find feature indices for a given plan design
const findFeatureIndices = (
  objectIndex: number,
  featureIdMap: IdMap,
  itemIdMap: IdMap,
  formMethods: any
): FeatureIndices | null => {
  const planDesign = formMethods.getValues(
    `${PLAN_DESIGNS_BASE_PATH}[${objectIndex}]`
  );
  if (!planDesign) return null;

  const planFeatures = planDesign?.plan_features;
  if (!planFeatures) return null;

  for (
    let featureIndex = 0;
    featureIndex < planFeatures.length;
    featureIndex++
  ) {
    const feature = planFeatures[featureIndex];
    if (feature[featureIdMap.key] === featureIdMap.value) {
      const featureItems = feature?.plan_feature_items;
      if (!featureItems) continue;

      for (let itemIndex = 0; itemIndex < featureItems.length; itemIndex++) {
        if (featureItems[itemIndex]?.[itemIdMap.key] === itemIdMap.value) {
          return { featureIndex, itemIndex };
        }
      }
    }
  }
  return null;
};

// Extract feature item and ID from the form path
const getBasePath = (path: string) => {
  const firstArrayIndex = path.indexOf('[');
  if (firstArrayIndex === -1) return path;

  // Find the path to the object containing the array
  const basePath = path.substring(0, firstArrayIndex);
  const lastArrayIndex = path.lastIndexOf(']');
  if (lastArrayIndex === -1) return basePath;

  // Return everything up to and including the last bracket
  return path.substring(0, lastArrayIndex + 1);
};

// Get item idMap from name
export const getIdMap = (name: string, formMethods: UseFormReturn<any>) => {
  const basePath = getBasePath(name);

  const item = formMethods?.getValues?.(basePath) as PlanFeatureItem;

  return getItemIdMap(item);
};

export const getFeatureIdMap = (feature: PlanFeature): IdMap | null => {
  if (!feature) return null;
  return feature.product_class_feature_id
    ? {
        key: 'product_class_feature_id',
        value: feature.product_class_feature_id,
      }
    : feature.product_feature_id
    ? { key: 'product_feature_id', value: feature.product_feature_id }
    : null;
};

export const getItemIdMap = (item: PlanFeatureItem): IdMap | null => {
  if (!item) return null;
  return item.product_class_feature_item_id
    ? {
        key: 'product_class_feature_item_id',
        value: item.product_class_feature_item_id,
      }
    : item.product_feature_item_id
    ? { key: 'product_feature_item_id', value: item.product_feature_item_id }
    : null;
};

const copyItemForFeature = (
  sourceItem: PlanFeatureItem,
  targetPlanFeatureId: number | undefined | null
): PlanFeatureItem => {
  const addedItem = { ...sourceItem };
  delete (addedItem as any).plan_feature_item_id;
  addedItem.plan_feature_id = targetPlanFeatureId;
  if (addedItem.plan_feature_id === null) {
    delete addedItem.plan_feature_id;
  }
  addedItem.value = null; // Set to null for new items
  return addedItem;
};

const copyFeatureForDesign = (
  sourceFeature: PlanFeature,
  targetPlanDesignId: number | undefined
): PlanFeature => {
  const addedFeature = { ...sourceFeature };
  delete (addedFeature as any).plan_feature_id;
  addedFeature.plan_design_id = targetPlanDesignId;
  addedFeature.plan_feature_items = addedFeature.plan_feature_items.map(
    (item) => copyItemForFeature(item, null) // Since new feature has no id yet
  );
  return addedFeature;
};

const syncFeatureItems = (
  targetFeature: PlanFeature,
  sourceFeature: PlanFeature
): PlanFeature => {
  const sourceItems = sourceFeature.plan_feature_items || [];
  const updatedTargetItems: PlanFeatureItem[] = [];

  sourceItems.forEach((sourceItem) => {
    const sourceIdMap = getItemIdMap(sourceItem);
    if (!sourceIdMap) return;

    const matchingTargetItem = targetFeature.plan_feature_items?.find((i) => {
      const iMap = getItemIdMap(i);
      return (
        iMap && iMap.key === sourceIdMap.key && iMap.value === sourceIdMap.value
      );
    });

    if (matchingTargetItem) {
      updatedTargetItems.push(matchingTargetItem);
    } else {
      updatedTargetItems.push(
        copyItemForFeature(sourceItem, targetFeature.plan_feature_id)
      );
    }
  });

  targetFeature.plan_feature_items = updatedTargetItems;
  return targetFeature;
};

export const syncPlanDesignStructure = (
  targetDesign: PlanDesign,
  sourceDesign: PlanDesign
) => {
  const sourceFeatures = sourceDesign.plan_features || [];
  const updatedTargetFeatures: PlanFeature[] = [];

  sourceFeatures.forEach((sourceFeature) => {
    const sourceIdMap = getFeatureIdMap(sourceFeature);
    if (!sourceIdMap) return;

    const matchingTargetFeature = targetDesign.plan_features?.find((f) => {
      const fMap = getFeatureIdMap(f);
      return (
        fMap && fMap.key === sourceIdMap.key && fMap.value === sourceIdMap.value
      );
    });

    if (matchingTargetFeature) {
      updatedTargetFeatures.push(
        syncFeatureItems(matchingTargetFeature, sourceFeature)
      );
    } else {
      updatedTargetFeatures.push(
        copyFeatureForDesign(sourceFeature, targetDesign.plan_design_id)
      );
    }
  });

  targetDesign.plan_features = updatedTargetFeatures;

  // Cleanup null plan_feature_id in items
  updatedTargetFeatures.forEach((feature) => {
    feature.plan_feature_items.forEach((item) => {
      if (item.plan_feature_id === null) {
        delete item.plan_feature_id;
      }
    });
  });
};

// Add at the top after imports
export const WATCHED_ANCILLARY_FIELDS = [
  'effective_date',
  'expiration_date',
  'eligibility_export_ind',
  'pharmacy_claim_export_ind',
  'notes',
];

// Add new function after syncPlanDesignStructure
export const syncPlanDesignAncillaries = (
  targetDesign: PlanDesign,
  sourceDesign: PlanDesign
) => {
  const sourceAncillaries = sourceDesign.plan_design_ancillaries || [];
  const updatedTargetAncillaries: any[] = [];

  // Keep existing that match
  (targetDesign.plan_design_ancillaries || []).forEach((targetAnc: any) => {
    const matchingSource = sourceAncillaries.find(
      (s: any) =>
        s.product_id === targetAnc.product_id &&
        s.product_options === targetAnc.product_options
    );
    if (matchingSource) {
      updatedTargetAncillaries.push(targetAnc);
    }
  });

  // Add missing from source
  sourceAncillaries.forEach((sourceAnc: any) => {
    const existsInTarget = updatedTargetAncillaries.some(
      (t: any) =>
        t.product_id === sourceAnc.product_id &&
        t.product_options === sourceAnc.product_options
    );
    if (!existsInTarget) {
      const addedAnc = { ...sourceAnc };
      delete (addedAnc as any).plan_design_ancillary_id;
      addedAnc.plan_design_id = targetDesign.plan_design_id;
      WATCHED_ANCILLARY_FIELDS.forEach((field) => {
        addedAnc[field] = null;
      });
      updatedTargetAncillaries.push(addedAnc);
    }
  });

  targetDesign.plan_design_ancillaries = updatedTargetAncillaries;
};

// Update syncAcrossPlanDesigns
export const syncAcrossPlanDesigns = (
  sync: boolean,
  toggleValue: boolean,
  name: string,
  value: string | number | null,
  formMethods: any
): void => {
  if (!formMethods || typeof formMethods.getValues !== 'function') {
    console.warn(
      'formMethods is undefined or missing getValues method in syncAcrossPlanDesigns'
    );
    return;
  }

  // If conditions aren't met, exit early
  if (!sync || toggleValue) return;

  let isAncillary = false;
  let parsed: any = null;
  let fieldName = '';

  // Parse for ancillary
  const ancillaryMatches = name.match(
    /plan_designs\[(\d+)\]\.plan_design_ancillaries\[(\d+)\]\.(.+)/
  );
  if (ancillaryMatches) {
    isAncillary = true;
    parsed = {
      planDesignIndex: parseInt(ancillaryMatches[1]),
      ancillaryIndex: parseInt(ancillaryMatches[2]),
    };
    fieldName = ancillaryMatches[3];
  } else {
    // Original feature parse
    const featureMatches = name.match(
      /plan_designs\[(\d+)\]\.plan_features\[(\d+)\]\.plan_feature_items\[(\d+)\]/
    );
    if (!featureMatches) return;
    parsed = {
      planDesignIndex: parseInt(featureMatches[1]),
      featureIndex: parseInt(featureMatches[2]),
      itemIndex: parseInt(featureMatches[3]),
    };
  }

  if (!parsed) return;

  const { planDesignIndex } = parsed;

  let matchCriteria: any = null;

  if (isAncillary) {
    const currentAncillaryPath = `${PLAN_DESIGNS_BASE_PATH}[${planDesignIndex}].plan_design_ancillaries[${parsed.ancillaryIndex}]`;
    const currentAncillary = formMethods.getValues(currentAncillaryPath);
    if (!currentAncillary) return;
    matchCriteria = {
      product_id: currentAncillary.product_id,
      product_options: currentAncillary.product_options,
    };
  } else {
    const currentFeaturePath = `${PLAN_DESIGNS_BASE_PATH}[${planDesignIndex}].plan_features[${parsed.featureIndex}]`;
    const currentFeature = formMethods.getValues(currentFeaturePath);
    if (!currentFeature) return;

    const featureIdMap = getFeatureIdMap(currentFeature);
    if (!featureIdMap) return;

    const itemIdMap = getIdMap(name, formMethods);
    if (!itemIdMap) return;

    matchCriteria = { featureIdMap, itemIdMap };
  }

  const planDesignIndices = getPlanDesignIndices(formMethods);

  planDesignIndices.forEach((designIndex) => {
    if (designIndex === planDesignIndex) return;

    let syncName = '';

    if (isAncillary) {
      const targetAncillariesPath = `${PLAN_DESIGNS_BASE_PATH}[${designIndex}].plan_design_ancillaries`;
      const targetAncillaries =
        formMethods.getValues(targetAncillariesPath) || [];
      let matchingIndex = -1;
      for (let i = 0; i < targetAncillaries.length; i++) {
        const anc = targetAncillaries[i];
        if (
          anc.product_id === matchCriteria.product_id &&
          anc.product_options === matchCriteria.product_options
        ) {
          matchingIndex = i;
          break;
        }
      }
      if (matchingIndex === -1) return;
      syncName = `${targetAncillariesPath}[${matchingIndex}].${fieldName}`;
    } else {
      const featureIndices = findFeatureIndices(
        designIndex,
        matchCriteria.featureIdMap,
        matchCriteria.itemIdMap,
        formMethods
      );
      if (!featureIndices) return;
      syncName = `${PLAN_DESIGNS_BASE_PATH}[${designIndex}].plan_features[${featureIndices.featureIndex}].plan_feature_items[${featureIndices.itemIndex}].value`;
    }

    const toggleName = `${syncName}_differs`;
    const targetToggleValue = formMethods.getValues(toggleName);

    if (targetToggleValue !== true) {
      formMethods.setValue(syncName, value, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    }
  });
};
