import { Flex } from '@chakra-ui/react';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
// import { createPlanDesignSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { normalizePlanDesignFieldPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/filterUtils';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React, { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  ACCUMULATORS_GENERAL_ITEM,
  GENERAL_ITEM,
  PLAN_DESIGN_LIST_ITEM,
  STANDARD_ITEM,
  XML_ITEM,
} from '../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../Navigation/uiContextEnum';
import { productNames } from '../../productNameConstants';
import { useGeneralForm } from './generalForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const GeneralComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();
  const productName = formMethods?.watch('plan.product.name');
  const planDesignIndex = getIndexFromURL();

  /**
   * VALIDATION IMPLEMENTATION GUIDE FOR DEVELOPERS:
   *
   * 1. GET VALIDATION DATA FOR YOUR PAGE:
   *    Use `useValidateByPageFromContext(YOUR_PAGE_CONSTANT)` where YOUR_PAGE_CONSTANT
   *    is the navigation constant for your specific page (e.g., GENERAL_ITEM, XML_ITEM, etc.)
   *
   *    This hook returns:
   *    - refetch: Function to manually refresh validation data
   *    - validationData: Current validation state for your page (errors, status, etc.)
   *    - isLoading: Boolean indicating if validation is currently being fetched
   *
   * 2. USING VALIDATION DATA:
   *    The validationData contains:
   *    - errors: Array of validation errors for your page
   *    - status: Validation status
   *    - validation_results: Detailed validation information
   *
   *    Use this data to:
   *    - Show/hide error messages
   *    - Enable/disable navigation buttons
   *    - Display validation status indicators
   */
  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(GENERAL_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(
    GENERAL_ITEM,
    'plan_design',
    planDesignIndex
  );

  const uiContextIndex = getUIContextFromNavigationConstant(GENERAL_ITEM);

  //   const baseSaveHandler = useSaveChangeRequestHandler(formMethods);
  //   const submitHandler = createPlanDesignSubmitHandler(baseSaveHandler);

  //   const handleSaveAndExit = useCallback(() => {
  //     const currentValues = getValues();
  //     submitHandler(currentValues);
  //   }, [getValues, submitHandler]);

  const { subCategories } = useGeneralForm(
    currentDetails as Partial<OrganizationDetails>
  );

  /**
   * CONTINUE HANDLER IMPLEMENTATION GUIDE:
   *
   * 1. SETUP THE CONTINUE HANDLER HOOK:
   *    Use `useContinueHandler` and pass:
   *    - refetch: The refetch function from useValidateByPageFromContext
   *    - onUpdateActiveItem: Navigation function to change pages
   *    - formMethods: React Hook Form methods for saving data
   *
   * 2. CREATE PAGE-SPECIFIC CONTINUE HANDLER:
   *    Call `createContinueHandler(CURRENT_PAGE, NEXT_PAGE)` where:
   *    - CURRENT_PAGE: Navigation constant for the page you're on (e.g., GENERAL_ITEM)
   *    - NEXT_PAGE: Navigation constant for where to navigate next (e.g., XML_ITEM)
   *
   * 3. WHAT HAPPENS ON CONTINUE:
   *    When user clicks continue, the handler:
   *    a) Saves current form data using auto-save
   *    b) Calls refetch() to get fresh validation data from API
   *    c) Waits 100ms for state to update
   *    d) Uses the page constant to get the correct UI context index
   *    e) Checks for validation errors in the refreshed data
   *    f) If no errors, navigates to the next page
   *    g) If errors exist, stays on current page (user can see validation issues)
   *
   * 4. VALIDATION DATA UPDATES:
   *    - During refetch, isLoading becomes true
   *    - After refetch completes, validationData is updated with fresh API data
   *    - isLoading becomes false
   *    - Your component re-renders with new validation state
   *
   * EXAMPLE FOR OTHER PAGES:
   * ```typescript
   * // For XML page navigating to Standard page:
   * const { refetch, validationData, isLoading } = useValidateByPageFromContext(XML_ITEM);
   * const { createContinueHandler } = useContinueHandler({ refetch, onUpdateActiveItem, formMethods });
   * const handleContinue = createContinueHandler(XML_ITEM, STANDARD_ITEM);
   * ```
   */
  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  // Create the continue handler for this specific page
  const handleContinue = createContinueHandler(
    GENERAL_ITEM,
    productName === productNames.ESI_360
      ? XML_ITEM
      : productName === productNames.CMK_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.IRX_360
      ? STANDARD_ITEM
      : ACCUMULATORS_GENERAL_ITEM
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(PLAN_DESIGN_LIST_ITEM);
    }
  };

  useEffect(() => {
    const UI_CONTEXT_INDEX_FOR_GENERAL =
      getUIContextFromNavigationConstant(GENERAL_ITEM);

    if (UI_CONTEXT_INDEX_FOR_GENERAL) {
      const currentPageValidation =
        validationData?.results?.[UI_CONTEXT_INDEX_FOR_GENERAL];

      if (!currentPageValidation) return;

      const { errors = [], warnings = [] } = currentPageValidation;

      // Handle errors
      errors.forEach(({ field, message }) => {
        const normalizedPath = normalizePlanDesignFieldPath(field, true);
        if (normalizedPath && message) {
          formMethods.setError(normalizedPath, {
            type: 'field-error',
            message,
          });
        }
      });

      // Handle warnings
      warnings.forEach(({ field, message }) => {
        const normalizedPath = normalizePlanDesignFieldPath(field, true);
        if (normalizedPath && message) {
          formMethods.setError(normalizedPath, {
            type: 'field-warning',
            message,
          });
        }
      });
    }
  }, [validationData, formMethods]);

  return (
    <Flex justify="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="General"
        formDescription=""
        subCategories={subCategories}
        onContinue={handleContinue}
        onBack={handleBack}
        // onSaveExit={handleSaveAndExit}
        isProcessing={isLoading}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default GeneralComponent;
