import {
  CostShareTier,
  ValidationResponse,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { formatDate as tableFormatDate } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/utils/tableUtils';
import axios from 'axios';
import { UseFormReturn } from 'react-hook-form';

/**
 * Shared utilities and constants for Cost Share components
 * This file centralizes common functionality to reduce redundancy and improve maintainability
 */

// Constants
export const PRE_PACKAGED_IND = {
  STANDARD: 0,
  UNBREAKABLE: 1,
} as const;

export const LESSER_GREATER_OPTIONS = {
  '1': 'Lesser',
  '2': 'Greater',
} as const;

export const INCLUDE_PBC_OPTIONS = {
  '1': 'Yes',
  '0': 'No',
} as const;

// Utility functions
export const getIndexFromURL = (): number => {
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    const index = urlParams.get('index');
    return index ? parseInt(index, 10) : 0;
  }
  return 0;
};

export const formatCurrency = (amount: any): string => {
  if (!amount && amount !== 0) return '--';
  const num = parseFloat(amount);
  return isNaN(num) ? '--' : `$${num.toFixed(2)}`;
};

export const formatPercentage = (pct: any): string => {
  if (!pct && pct !== 0) return '--';
  const num = parseFloat(pct);
  return isNaN(num) ? '--' : `${num.toFixed(1)}%`;
};

export const formatYesNo = (value: any): string => {
  const strValue = String(value);
  return strValue === '1' ? 'Yes' : strValue === '0' ? 'No' : '--';
};

// Custom date formatter to ensure MM/DD/YYYY format
export const formatDate = (value: any): string => {
  if (!value) return '--';
  const formatted = tableFormatDate(value, false); // false ensures MM/DD/YYYY format
  return formatted || '--';
};

/**
 * Generic function to get identifier from the API
 * @param type - The type of identifier to generate (e.g., 'cost_share_tier')
 * @returns Promise<string> - The generated identifier
 * @throws Error if the API call fails
 */
export const getIdentifier = async (type: string): Promise<string> => {
  try {
    const response = await axios.get(`/api/ben-admin/identifier?type=${type}`, {
      headers: { 'X-Request-Key': 'identifier' },
    });

    if (!response.data) {
      throw new Error('No identifier returned from API');
    }

    return response.data;
  } catch (error) {
    console.error(`Error fetching identifier for type ${type}:`, error);
    throw new Error(`Failed to generate identifier for ${type}`);
  }
};

// Type definitions
export interface CostShareTierBase {
  name: string | null;
  effective_date: string | null;
  expiration_date: string | null;
  tier_ind: string | null;
  pharmacy_channel_ind: string | null;
  days_supply: string | null;
  co_pay_amount: string | null;
  co_insurance_pct: string | null;
  co_insurance_ltgt_ind: string | null;
  co_insurance_min_amount: string | null;
  co_insurance_max_amount: string | null;
  network_status_ind: string | null;
  pre_packaged_ind: string | number | null;
  drug_list_ind: string | null;
  include_pbc_ind: string | null;
}

// Form field path generator
export const getFieldPath = (planDesignIndex: number): string =>
  `plan.plan_designs[${planDesignIndex}].plan_design_details[0].cost_share_tiers`;

// Create empty cost share tier
export const createEmptyCostShareTier = (
  prePackagedIndFilter: number,
  name?: string
): Partial<CostShareTierBase> => ({
  name: name || null,
  effective_date: null,
  expiration_date: null,
  tier_ind: null,
  pharmacy_channel_ind: null,
  days_supply: null,
  co_pay_amount: null,
  co_insurance_pct: null,
  co_insurance_ltgt_ind: null,
  co_insurance_min_amount: null,
  co_insurance_max_amount: null,
  network_status_ind: null,
  pre_packaged_ind: prePackagedIndFilter.toString(),
  drug_list_ind: null,
  include_pbc_ind: null,
});

// Transform tier data for table display
export const transformTierForDisplay = (tier: any, index: number) => ({
  _index: index,
  ...tier,
});

// Filter tiers by pre-packaged indicator
export const filterTiersByPrePackaged = (
  tiers: any[],
  prePackagedIndFilter: number
) =>
  tiers.filter(
    (tier) => Number(tier.pre_packaged_ind) === Number(prePackagedIndFilter)
  );

/**
 * Generate a unique copy name for duplicated tiers
 * @param originalName - The original name of the tier
 * @param existingTiers - Array of existing tiers to check against
 * @returns string - The unique copy name
 */
export const generateCopyName = (
  originalName: string,
  existingTiers: any[]
): string => {
  if (!originalName) return 'Copy';

  const baseName = originalName;
  const existingNames = existingTiers.map((tier) => tier.name).filter(Boolean);

  // Check if name already has (COPY) or (COPY n) pattern
  const copyRegex = /^(.*?)\s*\(COPY(?:\s+(\d+))?\)$/;
  const match = baseName.match(copyRegex);

  const nameWithoutCopy = match ? match[1].trim() : baseName;
  let copyNumber = match && match[2] ? parseInt(match[2], 10) : 0;

  // Generate unique name
  let newName =
    copyNumber === 0
      ? `${nameWithoutCopy} (COPY)`
      : `${nameWithoutCopy} (COPY ${copyNumber})`;

  // Keep incrementing until we find a unique name
  while (existingNames.includes(newName)) {
    copyNumber++;
    newName = `${nameWithoutCopy} (COPY ${copyNumber})`;
  }

  return newName;
};

// Duplicate tier with intelligent copy naming
export const duplicateTier = (tier: any, existingTiers: any[] = []) => {
  const copyName = generateCopyName(tier.name || 'Tier', existingTiers);

  return {
    ...tier,
    name: copyName,
    justDuplicated: true,
  };
};

// Get Days Supply value for Generate Prepackage Copay Tier
export const getDaysSupplyForFirstPrepackageTier = (
  prepackageCopay: string,
  originalDaysSupply: string
) => {
  switch (prepackageCopay) {
    case '2':
      return '2X';
    case '3':
      return '3X';
    case '4':
      return '2.5X';
    case '5':
      return '01-90';
    case '6':
      return '01-90';
    default:
      return originalDaysSupply;
  }
};

export const getDaysSupplyForSecondPrepackageTier = (
  originalDaysSupply: string
) => {
  switch (originalDaysSupply) {
    case '01-30':
      return 'DM30';
    case '01-31':
      return 'DM31';
    case '01-34':
      return 'DM34';
    default:
      return originalDaysSupply;
  }
};

// Form validation helpers
export const validateCurrency = (value: any): boolean => {
  if (!value && value !== 0) return true; // Allow empty values
  const num = parseFloat(value);
  return !isNaN(num) && num >= 0;
};

export const validatePercentage = (value: any): boolean => {
  if (!value && value !== 0) return true; // Allow empty values
  const num = parseFloat(value);
  return !isNaN(num) && num >= 0 && num <= 100;
};

// Common table configurations
export const TABLE_DIMENSIONS = {
  UNIFORM_COLUMN_WIDTH: '300px',
  ACTIONS_COLUMN_WIDTH: '60px',
} as const;

// Badge color schemes for different statuses
export const BADGE_COLOR_SCHEMES = {
  NETWORK_STATUS: {
    '1': 'green',
    '2': 'orange',
  },
  TIER: 'blue',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  INVALID_CURRENCY: 'Please enter a valid currency amount',
  INVALID_PERCENTAGE: 'Please enter a percentage between 0 and 100',
  REQUIRED_FIELD: 'This field is required',
} as const;

/**
 * Utility to extract a signature for a cost share tier (all fields except name)
 */
export function getTierSignature(tier: any): string {
  // Exclude 'name' and any internal fields (like _index)
  const { name, _index, _originalIndex, ...rest } = tier;
  // Stringify with sorted keys for stable comparison
  return JSON.stringify(
    Object.keys(rest)
      .sort()
      .reduce((acc, key) => {
        acc[key] = rest[key];
        return acc;
      }, {} as any)
  );
}

/**
 * Extracts the numeric part from a cost share tier name (e.g., CT0000008 => 8)
 */
export function extractTierNameNumber(name: string): number {
  const match = name && name.match(/(\d+)/);
  return match ? parseInt(match[1], 10) : Number.MAX_SAFE_INTEGER;
}

// Add validation error parsing utilities

export interface ValidationError {
  field: string;
  plan_validation_rule_id: number;
  rule_name: string;
  validation_message: string;
}

export interface ValidationWarning {
  field: string;
  plan_validation_rule_id: number;
  rule_name: string;
  validation_message: string;
}

export interface ValidationResult {
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface RowValidationState {
  hasErrors: boolean;
  hasWarnings: boolean;
  fieldErrors: Record<string, ValidationError>;
  fieldWarnings: Record<string, ValidationWarning>;
}

// Reusable function to process validations (errors or warnings)
function processValidations(
  items: any[],
  isError: boolean,
  planDesignIndex: number,
  prePackagedIndFilter: number,
  formMethods: UseFormReturn<any>,
  rowValidationMap: Map<number, RowValidationState>
) {
  items.forEach((item) => {
    const pathParts = item.field.split('.');
    // Remove only the last part (field name) to get the specific cost_share_tier object path
    const costShareTierPath = pathParts.slice(0, -1).join('.');
    const costShareTier = formMethods.getValues(
      'plan.' + costShareTierPath
    ) as CostShareTier;

    const prePackIndMatches =
      Number(costShareTier.pre_packaged_ind) === Number(prePackagedIndFilter);

    // Skip for tiers that don't match the current pre_packaged_ind filter
    if (!prePackIndMatches) {
      return;
    }

    const parsed = parseFieldPath(item.field, planDesignIndex);
    if (parsed) {
      const { rowIndex, fieldName } = parsed;

      if (!rowValidationMap.has(rowIndex)) {
        rowValidationMap.set(rowIndex, {
          hasErrors: false,
          hasWarnings: false,
          fieldErrors: {},
          fieldWarnings: {},
        });
      }

      const rowState = rowValidationMap.get(rowIndex)!;
      if (isError) {
        rowState.hasErrors = true;
        rowState.fieldErrors[fieldName] = item;
      } else {
        rowState.hasWarnings = true;
        rowState.fieldWarnings[fieldName] = item;
      }
    }
  });
}

export function parseValidationErrors(
  validationData: ValidationResponse | null,
  planDesignIndex: number,
  prePackagedIndFilter: number,
  formMethods: UseFormReturn<any>
): Map<number, RowValidationState> {
  const rowValidationMap = new Map<number, RowValidationState>();

  if (!validationData?.results) {
    return rowValidationMap;
  }

  try {
    // Process each validation result
    Object.keys(validationData.results).forEach((key) => {
      const result = validationData.results[key];
      if (!result?.validation_results) return;

      const { errors = [], warnings = [] } = result.validation_results;

      // Process validation_results errors and warnings
      processValidations(
        errors,
        true,
        planDesignIndex,
        prePackagedIndFilter,
        formMethods,
        rowValidationMap
      );
      processValidations(
        warnings,
        false,
        planDesignIndex,
        prePackagedIndFilter,
        formMethods,
        rowValidationMap
      );

      // Process top-level errors and warnings
      const topLevelErrors = result.errors || [];
      processValidations(
        topLevelErrors,
        true,
        planDesignIndex,
        prePackagedIndFilter,
        formMethods,
        rowValidationMap
      );
      const topLevelWarnings = result.warnings || [];
      processValidations(
        topLevelWarnings,
        false,
        planDesignIndex,
        prePackagedIndFilter,
        formMethods,
        rowValidationMap
      );
    });

    return rowValidationMap;
  } catch (error) {
    console.error('Error parsing validation data:', error);
    return rowValidationMap;
  }
}

/**
 * Parses a field path from validation response to extract row index and field name
 * Example: "plan_designs.0.plan_design_details.0.cost_share_tiers.2.tier_ind"
 * Returns: { rowIndex: 2, fieldName: "tier_ind" }
 */
function parseFieldPath(
  fieldPath: string,
  expectedPlanDesignIndex: number
): {
  rowIndex: number;
  fieldName: string;
} | null {
  // Expected pattern: plan_designs.{planIndex}.plan_design_details.{detailIndex}.cost_share_tiers.{tierIndex}.{fieldName}
  const pathRegex =
    /^plan_designs\.(\d+)\.plan_design_details\.(\d+)\.cost_share_tiers\.(\d+)\.(.+)$/;
  const match = fieldPath.match(pathRegex);

  if (!match) {
    return null;
  }

  // Fixed: properly destructure all 4 captured groups, skip unused detailIndex
  const [, planIndex, , tierIndex, fieldName] = match;
  const planIdx = parseInt(planIndex, 10);
  const tierIdx = parseInt(tierIndex, 10);

  // Only return if this matches our current plan design
  if (planIdx !== expectedPlanDesignIndex) {
    return null;
  }

  return {
    rowIndex: tierIdx,
    fieldName: fieldName,
  };
}

/**
 * Gets the validation state for a specific row
 */
export function getRowValidationState(
  validationMap: Map<number, RowValidationState>,
  rowIndex: number
): RowValidationState {
  return (
    validationMap.get(rowIndex) || {
      hasErrors: false,
      hasWarnings: false,
      fieldErrors: {},
      fieldWarnings: {},
    }
  );
}

/**
 * Checks if a specific field has validation errors or warnings
 */
export function getFieldValidationState(
  validationMap: Map<number, RowValidationState>,
  rowIndex: number,
  fieldName: string
): {
  hasError: boolean;
  hasWarning: boolean;
  error?: ValidationError;
  warning?: ValidationWarning;
} {
  const rowState = getRowValidationState(validationMap, rowIndex);

  const result = {
    hasError: !!rowState.fieldErrors[fieldName],
    hasWarning: !!rowState.fieldWarnings[fieldName],
    error: rowState.fieldErrors[fieldName],
    warning: rowState.fieldWarnings[fieldName],
  };

  return result;
}
