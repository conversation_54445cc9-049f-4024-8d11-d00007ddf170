'use client';
// src/components/ChangeRequestIntake/IntakeSidebar.tsx
import { Box } from '@chakra-ui/react';
import { SideNavigationBar } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/sideNavBar';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

interface IntakeSidebarProps {
  config: any;
  activeItemId: string;
  onSelectItem: (item: any) => void;
  formMethods: UseFormReturn<any>;
}

const IntakeSidebar: React.FC<IntakeSidebarProps> = ({
  config,
  activeItemId,
  onSelectItem,
  formMethods,
}) => {
  return (
    <Box as="nav" w="280px">
      <SideNavigationBar
        config={config}
        activeItemId={activeItemId}
        onSelectItem={onSelectItem}
        formMethods={formMethods}
      />
    </Box>
  );
};

export default IntakeSidebar;
