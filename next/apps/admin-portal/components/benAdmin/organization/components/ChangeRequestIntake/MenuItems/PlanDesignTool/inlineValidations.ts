export const ValidationFieldMapper = {
  error: 'field-error',
  warning: 'field-warning',
  normalizePath(path: string): string {
    return path
      .replace(/\[(\d+)\]/g, '.$1') // [0] to 0
      .replace(/^\./, '') // remove leading dot
      .toLowerCase();
  },

  extractFieldNamesFromSubCategories(subCategories: any[]): string[] {
    const fieldNames: string[] = [];

    for (const subCategory of subCategories) {
      for (const inlineGroup of subCategory.fields ?? []) {
        for (const field of inlineGroup.fields ?? []) {
          if (field?.name) {
            fieldNames.push(field.name);
          }
        }
      }
    }

    return fieldNames;
  },

  createNormalizedPathMap(fieldPaths: string[]): Map<string, string> {
    const map = new Map<string, string>();
    fieldPaths.forEach((path) => {
      map.set(this.normalizePath(path), path);
    });
    return map;
  },

  resolveField(
    apiField: string,
    pathMap: Map<string, string>
  ): string | undefined {
    const normalizedApi = this.normalizePath(apiField);
    for (const [normalized, original] of pathMap.entries()) {
      if (normalized.endsWith(normalizedApi)) {
        return original;
      }
    }
    return undefined;
  },
};
