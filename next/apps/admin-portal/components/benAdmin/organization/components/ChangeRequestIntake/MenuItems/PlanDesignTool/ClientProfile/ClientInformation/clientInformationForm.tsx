import {
  HelpText,
  OrganizationDetails,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { clientConfig } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/ClientProfile/Config/clientConfig';
import { useFormFieldsWithHelp } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useFormFieldsWithHelp';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { productNames } from '../../productNameConstants';

/**
 * useClientInformationForm Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param currentDetails - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories.
 */
export function useClientInformationForm(
  currentDetails: Partial<OrganizationDetails>,
  helpText?: HelpText
) {
  const {
    erisaMap,
    benefitPeriodsMap,
    terminationReasonMap,
    planClassMap,
    stateMap,
    yesNoMap,
  } = usePicklistMaps();

  // wrapper that populates infoText from the API for each formField
  const { defineFormFieldWithHelp } = useFormFieldsWithHelp(helpText);

  const productName = currentDetails?.plan?.product?.name;

  // Build the subcategories using the helper functions.
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Legal Name',
          'input',
          clientConfig.legal_entity_name,
          currentDetails?.organization?.legal_entity_name,
          {
            placeholder: 'Enter legal name',
            validations: z
              .string()
              .max(100, 'Organization name must be less than 100 characters'),
          }
        ),
        defineFormFieldWithHelp(
          'Employer Address',
          'input',
          clientConfig.address_line_1,
          currentDetails?.organization?.address?.address_line_1,
          {
            placeholder: 'Enter employer address',
            validations: z
              .string()
              .max(255, 'Address must be less than 255 characters'),
          }
        ),

        defineFormFieldWithHelp(
          'Employer Address 2',
          'input',
          clientConfig.address_line_2,
          currentDetails?.organization?.address?.address_line_2,
          {
            placeholder: 'Enter employer address 2',
            isRequired: false,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Employer Address City',
          'input',
          clientConfig.city,
          currentDetails?.organization?.address?.city,
          {
            placeholder: 'Enter employer city',
            isRequired: false,
          }
        ),
        defineFormFieldWithHelp(
          'Employer Address State',
          'dropdownSelect',
          clientConfig.state,
          currentDetails?.organization?.address?.state,
          {
            placeholder: 'Enter employer state',
            isRequired: false,
            optionsMap: stateMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Employer Address Zip',
          'input',
          clientConfig.postal_code,
          currentDetails?.organization?.address?.postal_code,
          {
            placeholder: 'Enter employer zip',
            isRequired: false,
          }
        ),
        defineFormFieldWithHelp(
          'Employer Address Country',
          'input',
          clientConfig.country,
          currentDetails?.organization?.address?.country,
          {
            placeholder: 'Enter employer country',
            isRequired: false,
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        (() => {
          const planBenefitPeriod =
            currentDetails?.plan?.plan_designs?.[0]?.plan_design_details?.[0]
              ?.benefit_period_ind;

          // Check if all plan designs have the same benefit period
          const allMatch = currentDetails?.plan?.plan_designs?.every((design) =>
            design?.plan_design_details?.every(
              (detail) => detail?.benefit_period_ind == planBenefitPeriod
            )
          );

          if (allMatch) {
            return defineFormFieldWithHelp(
              'Benefit Period',
              'dropdownSelect',
              clientConfig.benefit_period_ind,
              planBenefitPeriod,
              {
                optionsMap: benefitPeriodsMap,
                isDisabled: true,
              }
            );
          } else {
            return defineFormFieldWithHelp(
              'Benefit Period',
              'input',
              clientConfig.benefit_period_ind,
              'View on Benefit',
              {
                isDisabled: true,
              }
            );
          }
        })(),
        defineFormFieldWithHelp(
          'Plan Year/Renewal',
          'dropdownSelect',
          clientConfig.plan_year_renewal,
          currentDetails?.plan?.plan_year_renewal,
          {
            optionsMap: benefitPeriodsMap,
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Plan Class',
          'dropdownSelect',
          clientConfig.plan_class_ind,
          currentDetails?.plan?.plan_class_ind,
          {
            optionsMap: planClassMap,
            infoText: 'Most clients will be "self-funded"',
            placeholder: 'Enter plan class',
          }
        ),
        defineFormFieldWithHelp(
          'ERISA Status',
          'dropdownSelect',
          clientConfig.erisa_ind,
          currentDetails?.plan?.erisa_ind,
          {
            optionsMap: erisaMap,
            infoText:
              'ERISA is governing law for self-funded plans. Clients who are normally non-ERISA are schools or government agencies.',
            placeholder: 'Select an Option',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Previous PBM',
          'input',
          clientConfig.previous_pbm,
          currentDetails?.plan?.previous_pbm,
          {
            placeholder: 'Enter previous PBM',
            validations: z
              .string()
              .max(100, 'Previous PBM cannot exceed 100 characters')
              .optional(),
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Plan Expiration Date',
          'datepicker',
          clientConfig.expiration_date,
          currentDetails?.plan?.expiration_date,
          {
            infoText: 'Plan Expiration Date',
            placeholder: 'Select Plan Expiration Date',
            validations: z.date(),
          }
        ),
        defineFormFieldWithHelp(
          'Plan Expiration Reason',
          'dropdownSelect',
          clientConfig.expiration_reason,
          currentDetails?.plan?.expiration_reason,
          {
            infoText: 'Plan Expiration Reason',
            placeholder: 'Select Plan Expiration Reason',
            optionsMap: terminationReasonMap, // need to check options for all
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Primary Termination Reason',
          'dropdownSelect',
          clientConfig.primary_termination_reason,
          currentDetails?.organization?.primary_termination_reason,
          {
            placeholder: 'Primary Reason of Termination',
            optionsMap: terminationReasonMap,
          }
        ),
        defineFormFieldWithHelp(
          'Secondary Termination Reason',
          'dropdownSelect',
          clientConfig.secondary_termination_reason,
          currentDetails?.organization?.secondary_termination_reason,
          {
            placeholder: 'Secondary Reason of Termination',
            optionsMap: terminationReasonMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Termination Details',
          'input',
          clientConfig.termination_notes,
          currentDetails?.organization?.termination_notes,
          {
            placeholder: 'Details of Termination',
            validations: z
              .string()
              .max(32768, 'Termination Details cannot exceed 32768 characters'),
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Does Client have Inhouse Pharmacy?',
          'dropdownSelect',
          clientConfig.inhouse_pharmacy_ind,
          currentDetails?.plan?.inhouse_pharmacy_ind,
          {
            placeholder: 'Does Client have Inhouse Pharmacy?',
            optionsMap: yesNoMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Is Client a 340B entity?',
          'dropdownSelect',
          clientConfig.is_340b_ind,
          currentDetails?.plan?.is_340b_ind,
          {
            placeholder: 'Is Client a 340B entity?',
            optionsMap: yesNoMap,
          }
        ),
      ]),

      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Company Head Quarter State',
          'dropdownSelect',
          clientConfig.company_hq_state_ind,
          currentDetails?.organization?.company_hq_state_ind,
          {
            isRequired: true,
            placeholder: 'Company Head Quarter State',
            optionsMap: stateMap,
          }
        ),
      ]),
      ...(productName === productNames.CMK_360 ||
      productName === productNames.ESI_360 ||
      productName === productNames.OPT_360 ||
      productName === productNames.IRX_360 ||
      productName === productNames.CMK_PROTECT_RX
        ? [
            defineInlineFieldGroup([
              defineFormFieldWithHelp(
                'PDX updates after signature',
                'textarea', // text or textarea
                clientConfig.pdx_updates_after_signature,
                currentDetails?.plan?.pdx_updates_after_signature,
                {
                  infoText: 'Enter PDX updates after signature',
                  placeholder: 'PDX updates after signature',
                  validations: z
                    .string()
                    .max(
                      2000,
                      'PDX updates after signature cannot exceed 2000 characters'
                    ),
                  rows: 5,
                  customProps: {
                    minHeight: '120px',
                    resize: 'none',
                    overflow: 'hidden',
                    style: {
                      minHeight: '120px',
                    },
                  },
                }
              ),
            ]),
          ]
        : []),
      defineInlineFieldGroup([
        defineFormFieldWithHelp(
          'Plan Overview Note',
          'textarea',
          clientConfig.overview_notes,
          currentDetails?.plan?.overview_notes,
          {
            placeholder: 'Plan Overview Note',
            validations: z
              .string()
              .max(3000, 'Plan Overview Note cannot exceed 3000 characters'),
            rows: 5,
            customProps: {
              minHeight: '120px',
              resize: 'none',
              overflow: 'hidden',
              style: {
                minHeight: '120px',
              },
            },
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
