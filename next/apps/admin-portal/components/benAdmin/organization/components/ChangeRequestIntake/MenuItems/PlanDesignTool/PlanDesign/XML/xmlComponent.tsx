import { Flex } from '@chakra-ui/react';
import {
  //   useSaveChangeRequestHandler,
  useXMLForm,
} from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
// import { createPlanDesignSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { normalizePlanDesignFieldPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/filterUtils';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React, { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  ACCUMULATORS_GENERAL_ITEM,
  GENERAL_ITEM,
  STANDARD_ITEM,
  XML_ITEM,
} from '../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../Navigation/uiContextEnum';
import { productNames } from '../../productNameConstants';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const XMLComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();
  const productName = currentDetails?.plan?.product?.name;
  const planDesignIndex = getIndexFromURL();

  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(XML_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(
    XML_ITEM,
    'plan_design',
    planDesignIndex
  );

  const uiContextIndex = getUIContextFromNavigationConstant(XML_ITEM);

  //   const baseSaveHandler = useSaveChangeRequestHandler(formMethods);

  // Create an enhanced submit handler that handles multi-plan updates
  //   const submitHandler = createPlanDesignSubmitHandler(baseSaveHandler);

  // Handle save and exit action
  //   const handleSaveAndExit = useCallback(() => {
  //     const currentValues = getValues();
  //     submitHandler(currentValues);
  //   }, [getValues, submitHandler]);
  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useXMLForm(
    currentDetails as Partial<OrganizationDetails>
  );

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  const handleContinue = createContinueHandler(
    XML_ITEM,
    productName === productNames.CMK_360 ||
      productName === productNames.ESI_360 ||
      productName === productNames.OPT_360 ||
      productName === productNames.IRX_360
      ? STANDARD_ITEM
      : ACCUMULATORS_GENERAL_ITEM
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(GENERAL_ITEM);
    }
  };

  useEffect(() => {
    const UI_CONTEXT_INDEX = getUIContextFromNavigationConstant(XML_ITEM);

    if (UI_CONTEXT_INDEX) {
      const currentPageValidation = validationData?.results?.[UI_CONTEXT_INDEX];

      if (!currentPageValidation) return;

      const { errors = [], warnings = [] } = currentPageValidation;

      // Handle errors
      errors.forEach(({ field, message }) => {
        const normalizedPath = normalizePlanDesignFieldPath(field, true);
        if (normalizedPath && message) {
          formMethods.setError(normalizedPath, {
            type: 'field-error',
            message,
          });
        }
      });

      // Handle warnings
      warnings.forEach(({ field, message }) => {
        const normalizedPath = normalizePlanDesignFieldPath(field, true);
        if (normalizedPath && message) {
          formMethods.setError(normalizedPath, {
            type: 'field-warning',
            message,
          });
        }
      });
    }
  }, [validationData, formMethods]);

  return (
    <Flex justify="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="XML Leave Behind - Info Provided by ESI"
        formDescription=""
        subCategories={subCategories}
        // onSaveExit={handleSaveAndExit}
        onContinue={handleContinue}
        onBack={handleBack}
        isProcessing={isLoading}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default XMLComponent;
