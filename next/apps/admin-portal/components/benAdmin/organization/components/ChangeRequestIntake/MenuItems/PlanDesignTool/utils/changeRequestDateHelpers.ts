import type { ChangeRequest } from 'apps/admin-portal/components/benAdmin/Models/interfaces';

/**
 * Validates if a date string is in the correct format (YYYY-MM-DD) and is a valid date
 * @param dateString - The date string to validate
 * @returns boolean indicating if the date is valid
 */
export const isValidDateString = (dateString: string): boolean => {
  if (!dateString) return false;

  // Check format: YYYY-MM-DD
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateString)) return false;

  // Check if it's a valid date
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
};

/**
 * Formats a date to YYYY-MM-DD string format
 * @param date - Date object to format
 * @returns Formatted date string in YYYY-MM-DD format
 */
export const formatDateToString = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Retrieves the change request data from session storage
 * @returns ChangeRequest object or null if not found or invalid
 */
export const getChangeRequestFromSession = (): ChangeRequest | null => {
  try {
    const changeRequestData = sessionStorage.getItem('selectedChangeRequest');
    if (!changeRequestData) return null;

    const changeRequest: ChangeRequest = JSON.parse(changeRequestData);
    return changeRequest;
  } catch (error) {
    console.error('Error parsing change request from session storage:', error);
    return null;
  }
};

/**
 * Gets the effective date from the current change request
 * @returns The change request's target_effective_date or null if not available
 */
export const getChangeRequestEffectiveDate = (): string | null => {
  const changeRequest = getChangeRequestFromSession();

  if (!changeRequest?.target_effective_date) {
    return null;
  }

  // Validate the date format
  if (!isValidDateString(changeRequest.target_effective_date)) {
    console.warn(
      'Change request effective date is not in valid format:',
      changeRequest.target_effective_date
    );
    return null;
  }

  return changeRequest.target_effective_date;
};

/**
 * Gets the default effective date for a new plan design
 * Priority: 1. Change Request effective date, 2. Current date
 * @returns Date string in YYYY-MM-DD format
 */
export const getDefaultPlanDesignEffectiveDate = (): string => {
  // First try to get change request effective date
  const changeRequestDate = getChangeRequestEffectiveDate();

  if (changeRequestDate) {
    return changeRequestDate;
  }

  // Fallback to current date if no change request date
  const today = new Date();
  return formatDateToString(today);
};
