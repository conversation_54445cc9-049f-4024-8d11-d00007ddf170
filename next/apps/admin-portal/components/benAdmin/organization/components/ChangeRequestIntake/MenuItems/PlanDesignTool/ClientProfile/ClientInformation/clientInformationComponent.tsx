import { Box } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
import { useValidationErrorsForForm } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useValidationErrorsForForm';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  CLIENT_INFORMATION_ITEM,
  PHARMACY_BENEFITS_MANAGER_ITEM,
} from '../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../Navigation/uiContextEnum';
import { useValidateByPageFromContext } from '../../../../Validations/ValidationContext';
import { useClientInformationForm } from './clientInformationForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const ClientInformationComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { refetch, validationData, isLoading } = useValidateByPageFromContext(
    CLIENT_INFORMATION_ITEM
  );

  // Use this hook to get data to populate Help Center based on navigation constant
  const { helpCenterData } = useHelpCenter(CLIENT_INFORMATION_ITEM);
  const validationResults =
    validationData?.results[
      getUIContextFromNavigationConstant(CLIENT_INFORMATION_ITEM) as number
    ].validation_results;

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useClientInformationForm(
    currentDetails as Partial<OrganizationDetails>,
    // Pass helpCenterData into each form hook (used to populate infoText)
    helpCenterData
  );
  //   const submitHandler = useSaveChangeRequestHandler(formMethods);

  // When the user clicks "Continue", run any internal continue logic then update the active menu item.
  const handleContinue = () => {
    // Get current form values
    const currentValues = formMethods.getValues();

    // Add owner_type and owner_id to address if it exists
    if (currentValues?.organization?.address) {
      currentValues.organization.address.owner_type = 'L';
      currentValues.organization.address.owner_id =
        currentValues.organization?.legal_entity_id;
    }

    // Call the original continue handler with the modified values
    formMethods.setValue('organization', currentValues.organization);

    // Continue to next step
    createContinueHandler(
      CLIENT_INFORMATION_ITEM,
      PHARMACY_BENEFITS_MANAGER_ITEM
    )();
  };

  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();

  //     // Add owner_type and owner_id to address if it exists
  //     if (currentValues?.organization?.address) {
  //       currentValues.organization.address.owner_type = 'L';
  //       currentValues.organization.address.owner_id =
  //         currentValues.organization?.legal_entity_id;

  //       // Update the form values
  //       formMethods.setValue('organization', currentValues.organization);
  //     }

  //     submitHandler(currentValues);
  //   };

  const contextIndex = getUIContextFromNavigationConstant(
    CLIENT_INFORMATION_ITEM
  );

  useValidationErrorsForForm({
    subCategories,
    formMethods,
    validationData,
    contextIndex,
  });

  return (
    <Box display="flex" flexDirection="row" justifyContent="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="Client Information"
        formDescription=""
        subCategories={subCategories}
        onContinue={handleContinue}
        // onSaveExit={handleSaveAndExit}
        isProcessing={isLoading}
      />
      <HelpCenter
        helpContent={helpCenterData}
        validationResults={validationResults}
      />
    </Box>
  );
};

export default ClientInformationComponent;
