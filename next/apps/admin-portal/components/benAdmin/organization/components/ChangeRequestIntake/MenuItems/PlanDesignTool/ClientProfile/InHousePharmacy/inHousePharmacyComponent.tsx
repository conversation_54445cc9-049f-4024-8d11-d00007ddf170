// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { BASE_PATHS } from '../../../../../Tabs/PlanDesign/ClientProfile/Config/configs';
import {
  IMPLEMENTATION_ITEM,
  PHARMACY_BENEFITS_MANAGER_ITEM,
} from '../../../../Navigation/navigationConstants';
import { InHousePharmacyForm } from './inHousePharmacyForm';

const InHousePharmacyComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  //   const submitHandler = useSaveChangeRequestHandler(formMethods);

  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  const handleNavigation = (destination: string) => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(destination);
    }
  };

  return (
    <DynamicCollectionSection
      basePath={BASE_PATHS.PHARMACIES}
      formMethods={formMethods}
      generalForm={<InHousePharmacyForm formMethods={formMethods} />}
      title="In-House Pharmacy"
      description=""
      emptyMessage="You haven't added anything."
      skipMessage="If this Plan does not have any In-House Pharmacies, skip to the next step."
      isOptional={true}
      modalTitle="In-House Pharmacy Details"
      onBack={() => handleNavigation(PHARMACY_BENEFITS_MANAGER_ITEM)}
      onSkip={() => handleNavigation(IMPLEMENTATION_ITEM)}
      //   onSaveExit={handleSaveAndExit}
      customNameField="legal_entity.name"
    />
  );
};

export default InHousePharmacyComponent;
