import { Flex } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
import { useValidationErrorsForForm } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useValidationErrorsForForm';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  ID_CARDS_ITEM,
  TRANSITION_FILES_AND_DETAILS_ITEM,
} from '../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../Navigation/uiContextEnum';
import { useIdCardsForm } from './idCardsForm';

interface IdCardsComponentProps {
  formMethods: UseFormReturn<OrganizationDetails>;
  onUpdateActiveItem?: (id: string) => void;
}

const IdCardsComponent: React.FC<IdCardsComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(ID_CARDS_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(ID_CARDS_ITEM);

  const uiContextIndex = getUIContextFromNavigationConstant(ID_CARDS_ITEM);

  const { subCategories } = useIdCardsForm(
    currentDetails as Partial<OrganizationDetails>,
    formMethods
  );

  //   const submitHandler = useSaveChangeRequestHandler(formMethods);

  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  const handleContinue = createContinueHandler(
    ID_CARDS_ITEM,
    TRANSITION_FILES_AND_DETAILS_ITEM
  );

  const contextIndex = getUIContextFromNavigationConstant(ID_CARDS_ITEM);

  useValidationErrorsForForm({
    subCategories,
    formMethods,
    validationData,
    contextIndex,
  });

  return (
    <Flex justify="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="ID cards"
        formDescription="Captures who is responsible for production and distribution of member ID cards."
        subCategories={subCategories}
        onContinue={handleContinue}
        // onSaveExit={handleSaveAndExit}
        isProcessing={isLoading}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default IdCardsComponent;
