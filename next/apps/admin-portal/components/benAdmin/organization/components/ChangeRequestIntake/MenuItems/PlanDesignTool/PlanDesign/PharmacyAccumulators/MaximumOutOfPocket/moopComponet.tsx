import { Box, Flex } from '@chakra-ui/react';
import { getAccumsGeneralPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/accumsGeneralConfig';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  DEDUCTIBLE_ITEM,
  MAXIMUM_OUT_OF_POCKET_ITEM,
  OTHER_CAP_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../../Navigation/uiContextEnum';
import { useValidateByPageFromContext } from '../../../../../Validations/ValidationContext';
import { MoopFormModal } from './moopForm';

const MoopComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  //   const submitHandler = useSaveChangeRequestHandler(formMethods);
  const urlIndex = getIndexFromURL() || 0;

  const { refetch, validationData } = useValidateByPageFromContext(
    MAXIMUM_OUT_OF_POCKET_ITEM
  );

  const uiContextIndex = getUIContextFromNavigationConstant(
    MAXIMUM_OUT_OF_POCKET_ITEM
  );

  const { helpCenterData, isFetching } = useHelpCenter(
    MAXIMUM_OUT_OF_POCKET_ITEM,
    'plan_design',
    urlIndex
  );

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  // Create the continue handler for this specific page
  const handleContinue = createContinueHandler(
    MAXIMUM_OUT_OF_POCKET_ITEM,
    OTHER_CAP_ITEM
  );

  // Watch product name to pass into form
  const productName = formMethods.watch('plan.product.name');

  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  const handleNavigation = (destination: string) => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(destination);
    }
  };

  return (
    <Flex>
      <Box flex="3" pr={8}>
        <DynamicCollectionSection
          basePath={getAccumsGeneralPaths(urlIndex).accumulation_moop}
          formMethods={formMethods}
          generalForm={<MoopFormModal productName={productName} />}
          title="Pharmacy Accumulators - Maximum Out of Pocket"
          description=""
          emptyMessage="You haven't added anything."
          skipMessage="If this Plan does not have any Maximum Out of Pocket tiers, skip to the next step."
          isOptional={true}
          modalTitle="Pharmacy Accumulators - Maximum Out of Pocket"
          onBack={() => handleNavigation(DEDUCTIBLE_ITEM)}
          onContinue={handleContinue}
          //   onSaveExit={handleSaveAndExit}
        />
      </Box>
      <Box flex="1">
        <HelpCenter
          helpContent={helpCenterData}
          isLoading={isFetching}
          validationResults={
            uiContextIndex
              ? validationData?.results?.[uiContextIndex]?.validation_results
              : undefined
          }
        />
      </Box>
    </Flex>
  );
};

export default MoopComponent;
