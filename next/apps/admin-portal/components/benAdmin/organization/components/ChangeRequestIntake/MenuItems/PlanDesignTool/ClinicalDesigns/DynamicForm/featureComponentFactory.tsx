import { Flex } from '@chakra-ui/react';
import { Feature } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  HelpCenterContextParam,
  useHelpCenter,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import { useValidateByPageFromContext } from '../../../../Validations/ValidationContext';
import DynamicFeatureForm from './DynamicFeatureForm';

interface FeatureComponentProps {
  feature: Feature;
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
  nextItemId?: string;
  previousItemId?: string;
  priorAuthReviewFeature?: Feature;
}

/**
 * Component that renders a feature with form and help center
 */
export const FeatureComponent: React.FC<FeatureComponentProps> = ({
  feature,
  formMethods,
  onUpdateActiveItem,
  nextItemId,
  previousItemId,
  priorAuthReviewFeature,
}) => {
  const uiContextInd =
    feature?.product_class_feature_id || feature?.product_feature_id;

  // Determine which parameter name to use based on which feature ID exists
  const contextParamName: HelpCenterContextParam =
    feature?.product_class_feature_id
      ? HelpCenterContextParam.PRODUCT_CLASS_FEATURE_ID
      : HelpCenterContextParam.PRODUCT_FEATURE_ID;

  const { validationData, refetch, isLoading } = useValidateByPageFromContext(
    undefined,
    uiContextInd,
    contextParamName as string
  );

  const { helpCenterData, isFetching } = useHelpCenter(
    undefined,
    undefined,
    undefined,
    uiContextInd,
    contextParamName
  );

  if (!feature) {
    return null;
  }

  return (
    <Flex justify="space-between">
      <DynamicFeatureForm
        feature={feature}
        formMethods={formMethods}
        onUpdateActiveItem={onUpdateActiveItem}
        nextItemId={nextItemId}
        previousItemId={previousItemId}
        refetch={refetch}
        isLoading={isLoading}
        priorAuthReviewFeature={priorAuthReviewFeature}
      />
      <HelpCenter
        validationResults={
          uiContextInd
            ? validationData?.results?.[uiContextInd]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

/**
 * Factory function that creates a component for a given feature
 */
export function createFeatureComponent(
  feature: Feature,
  formMethods: UseFormReturn<any>,
  onUpdateActiveItem?: (id: string) => void,
  nextItemId?: string,
  previousItemId?: string,
  priorAuthReviewFeature?: Feature
): React.ReactNode {
  return (
    <FeatureComponent
      feature={feature}
      formMethods={formMethods}
      onUpdateActiveItem={onUpdateActiveItem}
      nextItemId={nextItemId}
      previousItemId={previousItemId}
      priorAuthReviewFeature={priorAuthReviewFeature}
    />
  );
}
