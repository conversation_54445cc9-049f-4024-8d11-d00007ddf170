// src/utils/sidebarConfig.tsx
// import { Text } from '@chakra-ui/react';
import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { UseFormReturn } from 'react-hook-form';
import { FiExternalLink, FiFile, FiHome, FiUser } from 'react-icons/fi';

import { SidebarConfig } from '../../../../ReusableComponents/Models/types';
import PreviewComponent from '../IntakeComponents/PreviewComponent';
import ContactsComponent from '../MenuItems/Contacts/contactsComponent';
import DocumentCenterComponent from '../MenuItems/DocumentCenter/documentCenterComponent';
import OverviewComponent from '../MenuItems/Overview/overviewComponent';
import {
  CLIENT_PROFILE_MODE,
  CLINICAL_DESIGN_MODE,
  MEMBER_EXPERIENCE_MODE,
  PLAN_DESIGN_MODE,
  PRODUCTS_AND_SERVICES_MODE,
} from './navigationConstants';

export const getMainSidebarConfig = (
  changeRequest: OrganizationDetails,
  formMethods: UseFormReturn<any>,
  navigateFn?: (section: string, tab?: string) => void, // Make navigateFn optional
  activeItemId?: string // Add optional activeItemId parameter for consistency
): SidebarConfig => {
  const disabled =
    !formMethods.watch(PLAN_DESIGNS_BASE_PATH) ||
    formMethods.watch(PLAN_DESIGNS_BASE_PATH).length === 0;
  return {
    sections: [
      {
        title: changeRequest?.organization?.name || 'Organization',
        items: [
          {
            id: 'overview',
            label: 'Overview',
            icon: FiHome,
            component: (
              <OverviewComponent
                changeRequest={changeRequest}
                formMethods={formMethods}
                onUpdateActiveItem={navigateFn} // Pass wrapper
              />
            ),
          },
          {
            id: 'contacts',
            label: 'Contacts',
            icon: FiUser,
            component: (
              <ContactsComponent
                formMethods={formMethods}
                changeRequest={changeRequest}
              />
            ),
          },
          {
            id: 'documents',
            label: 'Document Center',
            icon: FiFile,
            component: (
              <DocumentCenterComponent changeRequest={changeRequest} />
            ),
          },
          //  {
          //   id: 'activity',
          //   label: 'Activity',
          //   icon: FiRefreshCw,
          //   component: (                         //temporarily hide below sections
          //     <ActivityComponent
          //       changeRequest={changeRequest}
          //       formMethods={formMethods}
          //     />
          //   ),
          // },
          {
            id: 'preview',
            label: 'See Live Preview',
            icon: FiExternalLink,
            component: (
              <PreviewComponent
                changeRequest={changeRequest}
                formMethods={formMethods}
              />
            ),
          },
        ],
      },
      {
        title: 'PLAN SET UP TOOLS',
        items: [
          {
            id: 'plan-design-tool',
            label: 'Plan Design Tool',
            icon: FiExternalLink,
            hasDropdown: true,
            dropdownItems: [
              {
                id: CLIENT_PROFILE_MODE,
                label: 'Client Profile',
                modeChange: CLIENT_PROFILE_MODE,
              },
              {
                id: MEMBER_EXPERIENCE_MODE,
                label: 'Member Experience',
                modeChange: MEMBER_EXPERIENCE_MODE,
              },
              {
                id: PLAN_DESIGN_MODE,
                label: 'Plan Design',
                modeChange: PLAN_DESIGN_MODE,
              },
              {
                id: PRODUCTS_AND_SERVICES_MODE,
                label: 'Products and Services',
                modeChange: PRODUCTS_AND_SERVICES_MODE,
                disabled: disabled,
              },
              {
                id: CLINICAL_DESIGN_MODE,
                label: 'Clinical Designs',
                modeChange: CLINICAL_DESIGN_MODE,
                disabled: disabled,
              },
            ],
          },

          // temporarily hide below sections
          // {
          //   id: 'structure-tool',
          //   label: 'Structure Tool',
          //   icon: FiExternalLink,
          //   component: null,
          //   hasDropdown: true,
          //   dropdownItems: [
          //     {
          //       id: 'structure-tool-1',
          //       label: 'Structure Option 1',
          //       component: <Text>Structure Option 1</Text>,
          //     },
          //     {
          //       id: 'structure-tool-2',
          //       label: 'Structure Option 2',
          //       component: <Text>Structure Option 2</Text>,
          //     },
          //   ],
          // },
          // {
          //   id: 'pricing-tool',
          //   label: 'Pricing Tool',
          //   icon: FiExternalLink,
          //   component: null,
          //   hasDropdown: true,
          //   dropdownItems: [
          //     {
          //       id: 'pricing-tool-1',
          //       label: 'Pricing Option 1',
          //       component: <Text>Pricing Option 1</Text>,
          //     },
          //     {
          //       id: 'pricing-tool-2',
          //       label: 'Pricing Option 2',
          //       component: <Text>Pricing Option 2</Text>,
          //     },
          // ],
          //},
        ],
      },
    ],
  };
};
