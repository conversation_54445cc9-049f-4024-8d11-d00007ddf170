import {
  Box,
  <PERSON>ton,
  Flex,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  ModalClose<PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  Spacer,
} from '@chakra-ui/react';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import {
  XmlDocumentData,
  XmlFormValues,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import Toast from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/TabSections/Toast';
import React, { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { useGenerateXmlModalForm } from './hooks/useGenerateXmlModalForm';
import ReadOnlyForm from './ReadOnlyForm';
import XMLWarningModal from './XMLwarningModal';

interface GenerateXmlModalProps {
  isOpen: boolean;
  onClose: () => void;
  planId: number;
  readOnlyData?: XmlDocumentData | null;
  isReadOnly?: boolean;
  onXmlGenerated: () => void;
}

interface PrevSubmissionResponse {
  count: number | null;
}

const defaultValues: XmlFormValues = {
  submission_type: '',
  effective_date_of_change: null,
  case_number: '',
  process_mode_ind: '',
  submission_comments: '',
};

const GenerateXmlModal: React.FC<GenerateXmlModalProps> = ({
  isOpen,
  onClose,
  planId,
  readOnlyData,
  isReadOnly = false,
  onXmlGenerated,
}) => {
  const { subCategories } = useGenerateXmlModalForm();
  const { useApiMutation } = useBenAdmin();

  const [showWarning, setShowWarning] = useState(false);
  const [isCheckboxChecked, setIsCheckboxChecked] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const formMethods = useForm({
    mode: 'onChange',
    defaultValues,
  });

  const isFormValid = formMethods.formState.isValid;
  const submissionType = formMethods.watch('submission_type');
  const processMode = formMethods.watch('process_mode_ind');

  // Reset form on close to clear values
  const handleClose = useCallback(() => {
    formMethods.reset(defaultValues);
    setShowWarning(false);
    setIsCheckboxChecked(false);
    onClose();
  }, [formMethods, onClose]);

  // Set form values with readOnlyData when in read-only mode
  useEffect(() => {
    if (isReadOnly && readOnlyData) {
      formMethods.reset({
        submission_type: readOnlyData.submission_type ?? '',
        effective_date_of_change: readOnlyData.effective_date_of_change ?? null,
        case_number: readOnlyData.case_number ?? '',
        process_mode_ind: readOnlyData.process_mode_ind ?? '',
        submission_comments: readOnlyData.submission_comments ?? '',
      });
    } else {
      formMethods.reset(defaultValues);
    }
  }, [isReadOnly, readOnlyData, formMethods]);

  const generateXMLMutation = useApiMutation('esixml', 'POST', {
    onSuccess: () => {
      onXmlGenerated();
      Toast({
        title: 'Generate XML',
        description: 'XML File successfully generated',
        position: 'top-right',
      });
      handleClose();
    },
    onError: () => {
      Toast({
        title: 'Failed to Generate XML',
        description: 'Unable to generate XML File. Please try again.',
        status: 'error',
        position: 'top-right',
      });
    },
  });

  const prevSubmissionMutation = useApiMutation('bpl_transmission', 'GET', {
    onSuccess: (data: PrevSubmissionResponse) => {
      const records = data?.count ?? 0;
      if (records > 0) {
        setShowWarning(true);
      } else {
        const values = formMethods.getValues();
        generateXMLMutation.mutate({
          plan_id: planId,
          ...values,
        });
      }
    },
    onError: () => {
      Toast({
        title: 'Fetch Previous Submissions',
        description: 'Unable to get Previous Submissions. Please try again.',
        status: 'error',
        position: 'top-right',
      });
    },
  });

  const submitXml = (values: XmlFormValues) => {
    setIsSubmitting(true);
    generateXMLMutation.mutate(
      {
        plan_id: planId,
        ...values,
      },
      {
        onSettled: () => {
          setIsSubmitting(false);
          setShowWarning(false);
          setIsCheckboxChecked(false);
        },
      }
    );
  };

  const handleSubmit = formMethods.handleSubmit((values) => {
    if (processMode?.toLowerCase() === 's') {
      prevSubmissionMutation.mutate({
        queryParams: {
          plan_id: planId,
          effective_date: values?.effective_date_of_change,
        },
      });
    } else {
      generateXMLMutation.mutate({
        plan_id: planId,
        ...values,
      });
    }
  });

  return (
    <>
      <Modal
        size="5xl"
        isOpen={isOpen}
        returnFocusOnClose={!isReadOnly}
        onClose={handleClose}
        isCentered
      >
        <ModalOverlay />
        <ModalContent p={6}>
          <ModalHeader>
            {isReadOnly ? 'View XML Data' : 'Generate a new XML'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody px={6} pt={0} pb={4}>
            <Box p={2}>
              {isReadOnly && readOnlyData ? (
                <ReadOnlyForm
                  subCategories={subCategories}
                  readOnlyData={readOnlyData}
                />
              ) : (
                <GenericForm
                  formMethods={formMethods}
                  subCategories={subCategories}
                  formDescription=""
                  showButtons={false}
                  isInModal
                />
              )}
            </Box>
          </ModalBody>
          <ModalFooter px={6} pt={0}>
            <Flex w="100%" align="center">
              <Button
                variant="outline"
                color="green.500"
                borderColor="green.500"
                onClick={handleClose}
                isDisabled={
                  generateXMLMutation.isPending ||
                  prevSubmissionMutation.isPending
                }
              >
                Cancel
              </Button>
              <Spacer />
              {!isReadOnly && (
                <Button
                  colorScheme="green"
                  onClick={handleSubmit}
                  isLoading={
                    generateXMLMutation.isPending ||
                    prevSubmissionMutation.isPending
                  }
                  isDisabled={!isFormValid || !submissionType || !processMode}
                >
                  Generate XML
                </Button>
              )}
            </Flex>
          </ModalFooter>
        </ModalContent>
      </Modal>
      <XMLWarningModal
        isOpen={showWarning}
        onClose={() => setShowWarning(false)}
        isCheckboxChecked={isCheckboxChecked}
        setIsCheckboxChecked={setIsCheckboxChecked}
        onConfirm={() => submitXml(formMethods.getValues())}
        isSubmitting={isSubmitting}
      />
    </>
  );
};

export default GenerateXmlModal;
