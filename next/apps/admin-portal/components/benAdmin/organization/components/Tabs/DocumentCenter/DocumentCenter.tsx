import {
  <PERSON>,
  But<PERSON>,
  Flex,
  <PERSON>ing,
  <PERSON>u,
  <PERSON>u<PERSON>utton,
  MenuItem,
  MenuList,
  Text,
  Tooltip,
  useDisclosure,
} from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import {
  XmlDocumentData,
  XmlResponseData,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { ConfirmationPopup } from 'apps/admin-portal/components/benAdmin/organization/components/ConfirmationPopup';
import AutoTable from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table';
import {
  showMostRecentGeneratedDocument,
  truncateString,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/utils/tableUtils';
import Toast from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/TabSections/Toast';
import { AutoTableColumn } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { useParams } from 'next/navigation';
import { ChangeEvent, useRef, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { BiUpload } from 'react-icons/bi';
import { BsThreeDotsVertical } from 'react-icons/bs';

import { useIsESIProduct } from '../../../hooks/useIsESIHook';
import { useIsChangeRequestPage } from '../../../hooks/useOrganizationHooks';
import { useOrganizationModals } from '../../../hooks/useOrganizationModals';
import { PlanDesignSelect } from '../../PlanDesignSelectPopup';
import { planDesignWithIdMap } from '../PlanDesign/PlanDesign/FieldGroup/allPlanDesigns';
import ActionMenu from './ActionMenu';
import GenerateXmlModal from './GenerateXmlModal';
import { useIsGenerateEsiXml } from './hooks/useIsGenerateEsiXml';

interface Mapper {
  [key: string]: string;
}

interface Document {
  document_id: string | number;
  file_name: string;
  created_date: string;
  creator: {
    name: string;
  };
  file_type: string;
  internal_ind: number;
}

export const DocumentCenter = ({
  planId,
  formMethods,
}: {
  planId: number;
  formMethods?: UseFormReturn<any>;
}) => {
  const [selectedFile, setSelectedFile] = useState<File>();
  const [pbcPlanDesignGroups, setPbcPlanDesignGroups] = useState<any>([]);

  const [isXmlModalOpen, setXmlModalOpen] = useState(false);
  const [viewDocumentData, setViewDocumentData] =
    useState<XmlDocumentData | null>(null);
  const {
    isOpen: isConfirmationOpen,
    onOpen: onConfirmationOpen,
    onClose: onConfirmationClose,
  } = useDisclosure();

  const queryClient = useQueryClient();
  const params = useParams();
  const changeRequestId = params?.changeRequestId;
  const isChangeRequestPage = useIsChangeRequestPage();
  const isESIProduct = useIsESIProduct(formMethods ?? ({} as UseFormReturn));
  const isGenerateEsiXml = useIsGenerateEsiXml(
    formMethods ?? ({} as UseFormReturn)
  );
  const { useApiQuery, useApiMutation } = useBenAdmin();

  const {
    isPlanDesignSelectOpen,
    onPlanDesignSelectClose,
    onPlanDesignSelectOpen,
  } = useOrganizationModals();

  const { document: planDocuments } = useApiQuery([
    {
      key: 'document',
      queryParams: { plan_id: planId },
      options: {
        enabled: !!planId,
      },
    },
  ]);

  const getPlanDesignGroupsForPBCMutation = useApiMutation('pbc_plan', 'POST', {
    onSuccess: (data: any) => {
      setPbcPlanDesignGroups(data?.['groups_with_same_features & ancillary']);
      onPlanDesignSelectOpen();
    },
    onError: () => {
      return Toast({
        title: 'Generate PBC',
        description: 'Failed to retrieve Plan Design groups for PBC generation',
      });
    },
  });

  const generatePDXMutation = useApiMutation('pdx', 'POST', {
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['document'] });
      return Toast({
        title: 'Generate PDX',
        description: 'PDX File successfully generated',
        position: 'top-right',
      });
    },
    onError: () => {
      return Toast({
        title: 'Failed to Generate PDX',
        description: 'Unable to generate PDX File. Please try again.',
        status: 'error',
        position: 'top-right',
      });
    },
  });

  const generatePBCMutation = useApiMutation('pbc', 'POST', {
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['document'] });
      return Toast({
        title: 'Generate PBC',
        description: 'PBC File successfully generated',
        position: 'top-right',
      });
    },
    onError: () => {
      return Toast({
        title: 'Failed to Generate PBC',
        description: 'Unable to generate PBC File. Please try again.',
        status: 'error',
        position: 'top-right',
      });
    },
  });

  const uploadFileMutation = useApiMutation('document', 'POST', {
    onSuccess: async (data: any) => {
      if (!selectedFile) {
        throw new Error('No file selected');
      }

      const response = await fetch(data.s3_file_link, {
        method: 'PUT',
        body: selectedFile,
        headers: {
          'Content-Type': selectedFile.type,
        },
      });

      setSelectedFile(undefined);

      if (!response.ok) {
        await fetch(`/api/ben-admin/document/${data.document_id}`, {
          method: 'DELETE',
        });

        return Toast({
          title: 'File Did Not Upload',
          description: 'Failed to upload file to AWS. Please try again.',
          position: 'top-right',
          status: 'error',
        });
      } else {
        queryClient.invalidateQueries({ queryKey: ['document'] });
        return Toast({
          title: 'File Uploaded',
          description: 'This file has been successfully uploaded',
          position: 'top-right',
        });
      }
    },
    onError: async () => {
      return Toast({
        title: 'File Did Not Upload',
        description: 'Failed to upload file. Please try again.',
        position: 'top-right',
        status: 'error',
      });
    },
  });

  const fetchXMLDocumentMutation = useApiMutation('bpl_transmission', 'GET', {
    onSuccess: async (data: XmlResponseData[]) => {
      const responseData = data[0] || {};

      const mappedData: XmlDocumentData = {
        submission_type: String(responseData.submission_type_ind ?? ''),
        effective_date_of_change: String(responseData.effective_date ?? ''),
        case_number: String(responseData.sf_case_number ?? ''),
        process_mode_ind: String(responseData.process_mode_ind ?? ''),
        request_id: String(responseData.request_id ?? ''),
        status_code: String(responseData.status_code ?? ''),
        error_response: String(responseData.error_response ?? ''),
        submission_comments: String(responseData.comments ?? ''),
      };

      setViewDocumentData(mappedData);
      setXmlModalOpen(true);
    },
    onError: () => {
      Toast({
        title: 'View XML Details',
        description: 'Unable to get XML details.',
        status: 'error',
        position: 'top-right',
      });
    },
  });

  const allowedFileTypes = [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/pdf',
    'image/jpeg',
    'image/tiff',
    'image/png',
    'application/msonenote',
    'text/xml',
  ];

  const fileRef = useRef<HTMLInputElement | null>(null);

  const handleClick = () => {
    if (fileRef.current) {
      fileRef.current.click();
    }
  };

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) return;

    if (file.size > 50000000) {
      return Toast({
        title: 'File Did Not Upload',
        description:
          'File cannot be larger than 50 MB. Please select a different file.',
        position: 'top-right',
        status: 'error',
      });
    } else if (!allowedFileTypes.includes(file.type)) {
      return Toast({
        title: 'File Did Not Upload',
        description: 'File type not accepted. Please select a different file.',
        position: 'top-right',
        status: 'error',
      });
    } else {
      setSelectedFile(file);

      const payload = {
        plan_id: planId,
        file_name: file.name,
        file_type: file.type,
      };

      uploadFileMutation.mutate(payload);
    }
  };

  const assignGroups: Mapper = {
    grp0: 'Internal',
    grp1: 'External',
    grp3: 'Both',
  };

  const handleGeneratePDX = () => {
    if (isChangeRequestPage) {
      generatePDXMutation.mutate({
        queryParams: { change_request: true },
        change_request_id: Number(changeRequestId),
      });
    } else {
      generatePDXMutation.mutate({
        plan_id: planId,
      });
    }
  };

  const handleGeneratePBC = () => {
    if (Object.keys(planDesignWithIdMap)?.length > 0) {
      getPlanDesignGroupsForPBCMutation.mutate({ plan_id: planId });
    } else {
      generatePBCMutation.mutate({ plan_id: planId, plan_design_ids: [] });
    }
  };

  const handleGenerateXML = () => {
    if (isGenerateEsiXml) {
      setXmlModalOpen(true);
      setViewDocumentData(null);
    } else {
      onConfirmationOpen();
    }
  };

  const handleViewXMLDocument = (row: Document) => {
    if (!row?.document_id) return;
    fetchXMLDocumentMutation.mutate({
      queryParams: { document_id: row.document_id },
    });
  };

  const handleXmlGenerated = () => {
    // Delay invalidation slightly to avoid race conditions
    setTimeout(() => {
      queryClient.invalidateQueries({ queryKey: ['document'] });
    }, 200);
  };

  const columns: AutoTableColumn<any>[] = [
    {
      key: 'file_name',
      override: {
        headerName: 'Document Name',
        data: (row) => ({
          display: (
            <Tooltip label={row.file_name}>
              <Text>{truncateString(row.file_name, 75)}</Text>
            </Tooltip>
          ),
          value: row.file_name,
        }),
      },
    },
    {
      key: 'created_date',
      override: { headerName: 'Uploaded On', datatype: 'date' },
    },
    {
      key: 'creator.name',
      override: { headerName: 'Uploaded By' },
    },
    {
      key: 'file_type',
      override: { headerName: 'File Type', datatype: 'text' },
    },
    {
      key: 'group',
      override: {
        headerName: 'Group',
        datatype: 'text',
        data: (row) => ({ value: assignGroups[`grp${row?.internal_ind}`] }),
      },
    },
    {
      key: 'exemptedHeader',
      override: {
        data: (row) => ({
          display: (
            <ActionMenu
              menuKey={row.document_id}
              rowData={row}
              onView={handleViewXMLDocument}
              isShowView={
                isGenerateEsiXml && row.file_type === 'application/xml'
              }
              isLoading={
                fetchXMLDocumentMutation.isPending &&
                fetchXMLDocumentMutation.variables?.queryParams?.document_id ===
                  row.document_id
              }
            />
          ),
        }),
      },
    },
  ];

  return (
    <>
      <Box w={'98%'}>
        <Heading color="gray.600" size={isChangeRequestPage ? 'md' : 'lg'}>
          Document Center
        </Heading>
        <Flex gap={8}>
          <Text color="gray.600" pt={3} pb={3}>
            Access and view documents, or upload any that are relevant to this
            organization. File types allowed: Word, Excel, PDF, JPEG, TIFF, PNG,
            One, and XML. File size cannot be larger than 50 MB.
          </Text>
          <Flex justify="flex-end" gap={4}>
            <Menu>
              <MenuButton
                as={Button}
                variant="outline"
                colorScheme="green"
                rightIcon={<BsThreeDotsVertical />}
                isLoading={
                  generatePDXMutation.isPending ||
                  generatePBCMutation.isPending ||
                  getPlanDesignGroupsForPBCMutation.isPending
                }
              >
                Generate
              </MenuButton>
              <MenuList>
                <MenuItem onClick={handleGeneratePDX}>PDX</MenuItem>
                <MenuItem onClick={handleGeneratePBC}>PBC</MenuItem>
                {isESIProduct && (
                  <MenuItem onClick={handleGenerateXML}>XML</MenuItem>
                )}
              </MenuList>
            </Menu>
            <Button
              variant="outline"
              colorScheme="green"
              rightIcon={<BiUpload />}
              onClick={handleClick}
              isLoading={uploadFileMutation.isPending}
            >
              Upload File
            </Button>
            <input
              ref={fileRef}
              type="file"
              onChange={handleFileChange}
              style={{ display: 'none' }}
            />
          </Flex>
        </Flex>
        <AutoTable
          data={showMostRecentGeneratedDocument(planDocuments)}
          columns={columns}
        />
      </Box>

      {/* Confirmation Popup for non-ESI XML generation */}
      <ConfirmationPopup
        isOpen={isConfirmationOpen}
        onClose={onConfirmationClose}
        onConfirm={onConfirmationClose}
        alertHeader="XML Generation Restricted"
        alertBody="XML Generation only available for ESI Clients on Carrier Number 9391"
        confirmButtonText="Ok"
        hideCancelButton
      />

      <GenerateXmlModal
        isOpen={isXmlModalOpen}
        onClose={() => setXmlModalOpen(false)}
        planId={planId}
        readOnlyData={viewDocumentData}
        isReadOnly={!!viewDocumentData}
        onXmlGenerated={handleXmlGenerated}
      />

      {isPlanDesignSelectOpen && (
        <PlanDesignSelect
          planId={planId}
          isOpen={isPlanDesignSelectOpen}
          onClose={onPlanDesignSelectClose}
          planDesignsList={planDesignWithIdMap}
          pbcPlanDesignGroups={pbcPlanDesignGroups}
          generatePBCMutation={generatePBCMutation}
        />
      )}
    </>
  );
};
