import { Feature } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  buildPicklistsMap,
  getOptionsMap,
  mapFieldType,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/Configurable/fieldUtils';
import {
  buildFieldPath,
  findMatchingPlanFeatureItem,
  findMatchingPlanFeatures,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planNavigation';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

/**
 * Helper function to construct field configurations from feature items and their associated picklists
 * Uses shared utility functions from configurableFieldUtils.ts
 *
 * @param formData The complete form data containing features, picklists, and plan designs
 * @param feature The feature whose fields are being built
 * @returns An array of template field configurations
 */
export const getClinicalDesignFields = (
  formData: any,
  feature: Feature,
  planDesignIndex: number,
  priorAuthReviewFeature?: Feature
): Partial<TemplateFieldConfig>[] => {
  // Safety check to ensure we have valid input data
  if (!formData || !feature || !feature.feature_items) {
    return [];
  }
  const priorAuthFeatureSuffix = 'UtilizationManagementPriorAuthorization';
  const featureNameSuffix = feature.name.split('-')?.[1];
  const priorAuthFeatureName =
    featureNameSuffix === priorAuthFeatureSuffix && feature.name; // this will be in format of [PBM]-UtilizationManagementPriorAuthorization

  const isEligibleForFeatureMerge = feature.name === priorAuthFeatureName;
  let priorAuthReviewerFeatureFieldConfigs: Partial<TemplateFieldConfig>[] = [];
  // Get picklists map for quick lookup using the shared utility
  const picklistsMap = buildPicklistsMap(formData.picklists || []);

  // Map feature_items to field configurations
  const featureFieldConfigs = (planFeature: Feature) => {
    return planFeature.feature_items.map((item: any) => {
      // Find the matching plan feature in the plan data using the shared utility
      const matchingPlanFeatures = findMatchingPlanFeatures(
        formData.plan,
        planFeature,
        planDesignIndex
      );
      // Find the matching plan feature item with current value using the shared utility
      const matchingItem = findMatchingPlanFeatureItem(
        matchingPlanFeatures,
        item
      );

      // Determine field type based on field_type_label using the shared utility
      const fieldType = mapFieldType(item.field_type_label) as any;

      // Get options map for selection fields using the shared utility
      const optionsMap = getOptionsMap(picklistsMap, item);

      // Build the field path using the shared utility
      const fieldPath = buildFieldPath(matchingItem, planDesignIndex);

      // Get the current value from the plan structure
      const currentValue = matchingItem ? matchingItem.value : '';

      // Return the complete field configuration object
      return {
        label: item.label || '', // Use the label from the backend or empty string as fallback
        value: currentValue, // Use the extracted value
        name: fieldPath, // Use the generated field path
        type: fieldType, // Set the field type (input, dropdown, etc.)
        // Only include optionsMap if it exists and field type needs options
        ...(optionsMap &&
        ['dropdownSelect', 'checkboxGroup', 'radioGroup'].includes(fieldType)
          ? { optionsMap }
          : {}),
      };
    });
  };
  if (isEligibleForFeatureMerge && priorAuthReviewFeature) {
    priorAuthReviewerFeatureFieldConfigs = featureFieldConfigs(
      priorAuthReviewFeature
    );
  }
  const clinicalFeatureFieldConfigs = featureFieldConfigs(feature);
  return [
    ...priorAuthReviewerFeatureFieldConfigs,
    ...clinicalFeatureFieldConfigs,
  ];
};
