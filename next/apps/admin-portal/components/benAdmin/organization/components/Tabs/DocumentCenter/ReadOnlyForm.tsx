import { Box, Text, Textarea } from '@chakra-ui/react';
import { XmlDocumentData } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import React from 'react';

// Centralized Picklist Options
const picklistOptions: Record<string, { value: string; label: string }[]> = {
  process_mode_ind: [
    { value: 'D', label: 'Do not Send to ESI' },
    { value: 'S', label: 'Send to ESI' },
  ],
};

// Utility Function to get Label from Value
const getPicklistLabel = (
  fieldName: string,
  value: string | null | undefined
): string => {
  if (!value) return ''; // Handles null or undefined
  const options = picklistOptions[fieldName];
  if (!options) return value;
  const matched = options.find((opt) => opt.value === value);
  return matched ? matched.label : value;
};

// Props Interface
interface ReadOnlyFormProps {
  subCategories: any[];
  readOnlyData: XmlDocumentData;
}

const ReadOnlyForm: React.FC<ReadOnlyFormProps> = ({
  subCategories,
  readOnlyData,
}) => {
  const formatDate = (date: string | null | undefined): string =>
    date
      ? new Date(date).toLocaleString(undefined, { dateStyle: 'short' })
      : '';

  // Define extra fields
  const extraFields = [
    { label: 'Request ID', name: 'request_id' },
    { label: 'Status Code', name: 'status_code' },
    { label: 'Error Response', name: 'error_response' },
  ];

  // Simplify field extraction
  const allFields = [
    ...subCategories.flatMap((subCat) =>
      subCat.fields.flatMap((fieldGroup: { fields: any }) => fieldGroup.fields)
    ),
    ...extraFields,
  ];

  // Group fields for display, filtering out undefined fields
  const groupedFields = [
    [
      allFields.find((f) => f.name === 'submission_type'),
      allFields.find((f) => f.name === 'effective_date_of_change'),
    ].filter((f): f is { name: string; label: string } => !!f), // Type guard
    [allFields.find((f) => f.name === 'case_number')].filter(
      (f): f is { name: string; label: string } => !!f
    ),
    [allFields.find((f) => f.name === 'process_mode_ind')].filter(
      (f): f is { name: string; label: string } => !!f
    ),
    [
      allFields.find((f) => f.name === 'request_id'),
      allFields.find((f) => f.name === 'status_code'),
    ].filter((f): f is { name: string; label: string } => !!f),
    [allFields.find((f) => f.name === 'error_response')].filter(
      (f): f is { name: string; label: string } => !!f
    ),
    [allFields.find((f) => f.name === 'submission_comments')].filter(
      (f): f is { name: string; label: string } => !!f
    ),
  ].filter((group) => group.length > 0); // Keep only non-empty groups

  return (
    <Box p={6} bg="white" boxShadow="sm" mx="auto">
      {groupedFields.map((group, groupIndex) => (
        <Box
          key={groupIndex}
          mb={4}
          display="flex"
          flexWrap="wrap"
          gap={4}
          _last={{ mb: 0 }}
        >
          {group.map((field) => {
            const value = readOnlyData[field.name as keyof typeof readOnlyData];
            const isTextArea = [
              'submission_comments',
              'error_response',
            ].includes(field.name);

            return (
              <Box
                key={field.name}
                flex={group.length > 1 ? '1 1 45%' : '1 1 100%'}
                display="flex"
                flexDirection={isTextArea ? 'column' : 'row'}
                alignItems={isTextArea ? 'flex-start' : 'center'}
                gap={isTextArea ? 2 : 0}
              >
                <Text
                  fontWeight="bold"
                  color="gray.700"
                  whiteSpace="nowrap"
                  mr={isTextArea ? 0 : 2}
                >
                  {`${field.label || field.name.replace(/_/g, ' ')}:`}
                </Text>
                {isTextArea ? (
                  <Textarea
                    value={value || ''}
                    isReadOnly
                    resize="none"
                    minHeight="120px"
                    rows={4}
                    bg="gray.50"
                    borderColor="gray.300"
                    _hover={{ borderColor: 'gray.300' }}
                    _focus={{ borderColor: 'gray.300', boxShadow: 'none' }}
                    p={2}
                    flex="1"
                  />
                ) : (
                  <Text
                    color="gray.900"
                    p={2}
                    bg="gray.50"
                    border="1px solid"
                    borderColor="gray.300"
                    borderRadius="md"
                    minHeight="40px"
                    display="flex"
                    alignItems="center"
                    flex="1"
                  >
                    {field.name === 'effective_date_of_change'
                      ? formatDate(value)
                      : getPicklistLabel(field.name, value)}
                  </Text>
                )}
              </Box>
            );
          })}
        </Box>
      ))}
    </Box>
  );
};

export default ReadOnlyForm;
