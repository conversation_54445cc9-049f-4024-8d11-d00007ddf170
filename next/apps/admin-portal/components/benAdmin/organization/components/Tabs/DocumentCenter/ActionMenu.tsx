import {
  <PERSON><PERSON>,
  <PERSON>u,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON>ist,
  Portal,
  Spinner,
  Text,
} from '@chakra-ui/react';
import { PlanDocument } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { BiBuildings, BiFileFind, BiShow, BiTrash } from 'react-icons/bi';
import { BsDownload, BsThreeDotsVertical } from 'react-icons/bs';

import { useOrganizationModals } from '../../../hooks/useOrganizationModals';
import AssignToGroupPopup from '../../AssignToGroupPopup';
import { ConfirmationPopup } from '../../ConfirmationPopup';
import RenamePopup from '../../RenamePopup';
import useActionHandlers from './actionHandlers';

interface ActionMenuProps {
  menuKey: number;
  rowData: PlanDocument;
  onView?: (rowData: PlanDocument) => void;
  isLoading?: boolean;
  isShowView?: boolean;
}

const ActionMenu = ({
  menuKey,
  rowData,
  onView,
  isLoading = false,
  isShowView = false,
}: ActionMenuProps) => {
  const formMethods = useForm<{ file_name: string }>();

  const {
    isDeleteOpen,
    onDeleteOpen,
    onDeleteClose,
    isRenameOpen,
    onRenameOpen,
    onRenameClose,
    isAssignToGroupOpen,
    onAssignToGroupOpen,
    onAssignToGroupClose,
  } = useOrganizationModals();

  const {
    renameDocument,
    deleteDocument,
    isRenameLoading,
    isDeleteLoading,
    downloadDocument,
    isDownloading,
    assignDocumentToGroup,
    isAssigning,
  } = useActionHandlers(
    rowData,
    onDeleteClose,
    onRenameClose,
    onAssignToGroupClose
  );

  const handleDelete = async () => {
    await deleteDocument();
  };

  const handleView = useCallback(() => {
    onView?.(rowData);
  }, [onView, rowData]);

  const actionList = [
    {
      action: 'Download',
      icon: <BsDownload size="16px" />,
      handler: downloadDocument,
    },
    {
      action: 'Delete',
      icon: <BiTrash size="16px" />,
      handler: onDeleteOpen,
    },
    {
      action: 'Rename File',
      icon: <BiFileFind size="16px" />,
      handler: () => {
        onRenameOpen();
        formMethods.reset({ file_name: rowData.file_name });
      },
    },
    {
      action: 'Assign to Group',
      icon: <BiBuildings size="16px" />,
      handler: onAssignToGroupOpen,
    },
    ...(isShowView
      ? [
          {
            action: 'View Details',
            icon: <BiShow size="16px" />,
            handler: handleView,
          },
        ]
      : []),
  ];

  return (
    <>
      <Flex justify="flex-end">
        <Menu key={menuKey} placement="bottom-end">
          <MenuButton key={menuKey} disabled={isLoading || isDownloading}>
            {isLoading || isDownloading ? (
              <Spinner size="xs" />
            ) : (
              <BsThreeDotsVertical />
            )}
          </MenuButton>
          <Portal>
            <MenuList key={menuKey}>
              {actionList.map((menu) => (
                <MenuItem
                  key={`${menuKey}-${menu.action}`}
                  icon={menu.icon}
                  onClick={() => menu.handler?.()}
                >
                  <Text color="#69696A" fontWeight="400" textStyle="sm">
                    {menu.action}
                  </Text>
                </MenuItem>
              ))}
            </MenuList>
          </Portal>
        </Menu>
      </Flex>

      <RenamePopup
        isOpen={isRenameOpen}
        onClose={onRenameClose}
        formMethods={formMethods}
        fileName={rowData.file_name}
        onRename={renameDocument}
        isRenameLoading={isRenameLoading}
      />

      <AssignToGroupPopup
        isOpen={isAssignToGroupOpen}
        onClose={onAssignToGroupClose}
        defaultValue={{
          internal_ind: rowData?.internal_ind,
        }}
        onAssignToGroup={assignDocumentToGroup}
        isAssignLoading={isAssigning}
      />

      <ConfirmationPopup
        isOpen={isDeleteOpen}
        onClose={onDeleteClose}
        onConfirm={handleDelete}
        alertHeader="Delete Document"
        alertBody="Are you sure you want to delete this Document? This action cannot be undone."
        confirmButtonText="Delete Document"
        isConfirmLoading={isDeleteLoading}
      />
    </>
  );
};

export default ActionMenu;
