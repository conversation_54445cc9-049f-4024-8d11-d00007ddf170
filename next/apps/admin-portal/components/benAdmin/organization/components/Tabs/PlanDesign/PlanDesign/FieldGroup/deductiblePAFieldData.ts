import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { getDeductiblePAPath } from '../Config/deductiblePAConfig';

export function getDeductiblePAFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) return [];

  const planDesignDetails = planDesign?.plan_design_details || [];
  const deductibleTemplates: TemplateFieldGroup[] = [];

  const productName = planData?.plan?.product?.name;

  planDesignDetails.forEach((designDetail) => {
    const accumDeductibles = designDetail?.accumulation_deductible;
    if (!Array.isArray(accumDeductibles)) return;

    accumDeductibles.forEach((deductible, index) => {
      deductibleTemplates.push({
        subtitle: `Pharmacy Accumulator - Deductible - ${
          deductible.accums_tier_name || ''
        }`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          ...([
            productNames.CMK_360,
            productNames.ESI_360,
            productNames.OPT_360,
            productNames.IRX_360,
          ].includes(productName as productNames)
            ? [
                {
                  label: 'Does Deductible Apply?',
                  value: deductible?.apply_ind,
                  name: getDeductiblePAPath('apply_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.yesNoMap,
                },
              ]
            : []),
          ...(deductible.apply_ind === 1
            ? [
                ...([
                  productNames.CMK_360,
                  productNames.ESI_360,
                  productNames.OPT_360,
                  productNames.IRX_360,
                ].includes(productName as productNames)
                  ? [
                      {
                        label: 'Accums Tier Name',
                        value: deductible?.accums_tier_name,
                        name: getDeductiblePAPath('accums_tier_name', index),
                        type: 'input' as const,
                        placeholder: 'Enter Accums Tier Name',
                      },
                      {
                        label: 'Accums Tier Effective Date',
                        value: deductible?.effective_date,
                        name: getDeductiblePAPath('effective_date', index),
                        type: 'datepicker' as const,
                      },
                      {
                        label: 'Accums Tier End Date',
                        value: deductible?.expiration_date,
                        name: getDeductiblePAPath('expiration_date', index),
                        type: 'datepicker' as const,
                      },
                      {
                        label: 'Accums Tier PBC order',
                        value: deductible?.pbc_order,
                        name: getDeductiblePAPath('pbc_order', index),
                        type: 'input' as const,
                        placeholder: 'Enter Accums Tier PBC order',
                      },
                      {
                        label: 'Deductible Accumulation Period',
                        value: deductible?.accum_period_ind,
                        name: getDeductiblePAPath('accum_period_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.esiAccumPeriodMap,
                      },
                      {
                        label: 'Specify Deductible Accumulation Period',
                        value: deductible?.specify_accum_period_ind,
                        name: getDeductiblePAPath(
                          'specify_accum_period_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.benefitPeriodsMap,
                      },
                    ]
                  : []),
                ...(productName === productNames.ESI_360
                  ? [
                      {
                        label: 'Benefit Period Length',
                        value: deductible?.benefit_period_length_ind,
                        name: getDeductiblePAPath(
                          'benefit_period_length_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.benefitPeriodLengthMap,
                      },
                      {
                        label: 'Benefit Period Length - Other',
                        value: deductible?.benefit_period_length_other,
                        name: getDeductiblePAPath(
                          'benefit_period_length_other',
                          index
                        ),
                        type: 'input' as const,
                        placeholder: 'Enter Benefit Period Length - Other',
                      },
                    ]
                  : []),
                ...([
                  productNames.CMK_360,
                  productNames.ESI_360,
                  productNames.OPT_360,
                  productNames.IRX_360,
                ].includes(productName as productNames)
                  ? [
                      {
                        label: 'Do Priming Balances Apply?',
                        value: deductible?.priming_balances_ind,
                        name: getDeductiblePAPath(
                          'priming_balances_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoMap,
                      },
                      {
                        label: 'Carryover Phase',
                        value: deductible?.carryover_phase_ind,
                        name: getDeductiblePAPath('carryover_phase_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.carryoverPhaseMap,
                      },
                      {
                        label: 'Describe Carryover Phase',
                        value: deductible?.describe_carryover_phase,
                        name: getDeductiblePAPath(
                          'describe_carryover_phase',
                          index
                        ),
                        type: 'input' as const,
                        placeholder: 'Enter Describe Carryover Phase',
                      },
                      {
                        label: 'Deductible Integrated?',
                        value: deductible?.integrated_ind,
                        name: getDeductiblePAPath('integrated_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.integratedMap,
                      },
                      {
                        label: 'Deductible Embedded?',
                        value: deductible?.embedded_ind,
                        name: getDeductiblePAPath('embedded_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.embeddedMap,
                      },
                      {
                        label: 'Individual Plan Deductible Amount',
                        value: deductible?.individual_plan_amount,
                        name: getDeductiblePAPath(
                          'individual_plan_amount',
                          index
                        ),
                        type: 'input' as const,
                        placeholder: 'Enter Individual Plan Deductible Amount',
                      },
                      {
                        label: 'Family Plan Deductible Amount',
                        value: deductible?.family_plan_amount,
                        name: getDeductiblePAPath('family_plan_amount', index),
                        type: 'input' as const,
                        placeholder: 'Enter Family Plan Deductible Amount',
                      },
                      {
                        label: 'Employee + 1 Dependent Deductible Amount',
                        value: deductible?.employee_1_dep_amount,
                        name: getDeductiblePAPath(
                          'employee_1_dep_amount',
                          index
                        ),
                        type: 'input' as const,
                        placeholder:
                          'Enter Employee + 1 Dependent Deductible Amount',
                      },
                      {
                        label: 'Individual Deductible within Family Amount',
                        value: deductible?.individual_within_family_amount,
                        name: getDeductiblePAPath(
                          'individual_within_family_amount',
                          index
                        ),
                        type: 'input' as const,
                        placeholder:
                          'Enter Individual Deductible within Family Amount',
                      },
                      {
                        label:
                          'Does Deductible Apply to Maximum Out of Pocket?',
                        value: deductible?.apply_to_moop_ind,
                        name: getDeductiblePAPath('apply_to_moop_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoMap,
                      },
                      {
                        label: 'Deductible Applies to Retail, Mail & Paper',
                        value: deductible?.apply_retail_mail_paper_ind,
                        name: getDeductiblePAPath(
                          'apply_retail_mail_paper_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoMap,
                      },
                      {
                        label: 'Penalties Apply to Deductible',
                        value: deductible?.penalties_apply_ind,
                        name: getDeductiblePAPath('penalties_apply_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoViewBenefitMap,
                      },
                      {
                        label: 'Copay Apply During Deductible Phase',
                        value:
                          deductible?.copay_apply_during_deductible_phase_ind,
                        name: getDeductiblePAPath(
                          'copay_apply_during_deductible_phase_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoMap,
                      },
                      {
                        label:
                          'Does Deductible Apply to Brand Medications Only?',
                        value: deductible?.apply_brand_only_ind,
                        name: getDeductiblePAPath(
                          'apply_brand_only_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoMap,
                      },
                      {
                        label:
                          'Does Deductible Apply to Specialty Medications Only?',
                        value: deductible?.apply_specialty_only_ind,
                        name: getDeductiblePAPath(
                          'apply_specialty_only_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoMap,
                      },
                    ]
                  : []),
                ...(productName === productNames.ESI_360
                  ? [
                      {
                        label: 'Shared Indicator',
                        value: deductible?.shared_ind,
                        name: getDeductiblePAPath('shared_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.sharedIndicatorMap,
                      },
                      {
                        label: 'Drug Type Status',
                        value: deductible?.drug_type_status_ind,
                        name: getDeductiblePAPath(
                          'drug_type_status_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.drugTypeStatusMap,
                      },
                      {
                        label: 'Formulary Status',
                        value: deductible?.formulary_status_ind,
                        name: getDeductiblePAPath(
                          'formulary_status_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.formularyStatusMap,
                      },
                    ]
                  : []),
                ...([
                  productNames.CMK_360,
                  productNames.ESI_360,
                  productNames.OPT_360,
                  productNames.IRX_360,
                ].includes(productName as productNames)
                  ? [
                      {
                        label: 'Network Status',
                        value: deductible?.network_status_ind,
                        name: getDeductiblePAPath('network_status_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.networkStatusMap || {},
                      },
                    ]
                  : []),
                ...([
                  productNames.CMK_360,
                  productNames.OPT_360,
                  productNames.IRX_360,
                ].includes(productName as productNames)
                  ? [
                      {
                        label: 'Specialty Deductible',
                        value: deductible?.specialty_deductible_amount,
                        name: getDeductiblePAPath(
                          'specialty_deductible_amount',
                          index
                        ),
                        type: 'input',
                        placeholder: 'Enter Specialty Deductible',
                      },
                      {
                        label: 'Deductible Applies to Brand',
                        value: deductible?.apply_brand_ind,
                        name: getDeductiblePAPath('apply_brand_ind', index),
                        type: 'dropdownSelect',
                        placeholder: 'Deductible Applies to Brand',
                      },
                      {
                        label: 'Deductible Applies to Retail',
                        value: deductible?.apply_retail_ind,
                        name: getDeductiblePAPath('apply_retail_ind', index),
                        type: 'dropdownSelect',
                        placeholder: 'Deductible Applies to Retail',
                      },
                      {
                        label: 'Deductible Applies to Specialty',
                        value: deductible?.apply_specialty_ind,
                        name: getDeductiblePAPath('apply_specialty_ind', index),
                        type: 'dropdownSelect',
                        placeholder: 'Deductible Applies to Specialty',
                      },
                      {
                        label: 'Deductible Applies to Generic',
                        value: deductible?.apply_generic_ind,
                        name: getDeductiblePAPath('apply_generic_ind', index),
                        type: 'dropdownSelect',
                        placeholder: 'Deductible Applies to Generic',
                      },
                      {
                        label: 'Deductible Applies to Mail',
                        value: deductible?.apply_mail_ind,
                        name: getDeductiblePAPath('apply_mail_ind', index),
                        type: 'dropdownSelect',
                        placeholder: 'Deductible Applies to Mail',
                      },
                      {
                        label: 'Deductible Applies to Diabetic',
                        value: deductible?.apply_diabetic_ind,
                        name: getDeductiblePAPath('apply_diabetic_ind', index),
                        type: 'dropdownSelect',
                        placeholder: 'Deductible Applies to Diabetic',
                      },
                    ]
                  : []),
                ...(productName === productNames.ESI_360
                  ? [
                      {
                        label: 'Pharmacy Channel',
                        value: deductible?.pharmacy_channel_ind,
                        name: getDeductiblePAPath(
                          'pharmacy_channel_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.pharmacyChannelAccumMap,
                      },
                      {
                        label: 'Network Applicability',
                        value: deductible?.network_applicability_ind,
                        name: getDeductiblePAPath(
                          'network_applicability_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.networkApplicabilityMap || {},
                      },
                      {
                        label: 'Include Drug List',
                        value: deductible?.include_drug_list_ind,
                        name: getDeductiblePAPath(
                          'include_drug_list_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.accumDrugListMap,
                      },
                      {
                        label: 'Exclude Drug List',
                        value: deductible?.exclude_drug_list_ind,
                        name: getDeductiblePAPath(
                          'exclude_drug_list_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.accumDrugListMap,
                      },
                      {
                        label: 'Deductible Notes',
                        value: deductible?.notes,
                        name: getDeductiblePAPath('notes', index),
                        type: 'input' as const,
                        placeholder: 'Enter Deductible Notes',
                      },
                    ]
                  : []),
              ]
            : []),
        ],
      });
    });
  });

  return deductibleTemplates.filter(Boolean);
}
