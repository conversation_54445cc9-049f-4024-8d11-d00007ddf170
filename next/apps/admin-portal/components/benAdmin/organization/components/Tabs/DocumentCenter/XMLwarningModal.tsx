import {
  Button,
  Center,
  Checkbox,
  Icon,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalOverlay,
  Text,
  VStack,
} from '@chakra-ui/react';
import React from 'react';

interface XMLWarningModalProps {
  isOpen: boolean;
  onClose: () => void;
  isCheckboxChecked: boolean;
  setIsCheckboxChecked: (value: boolean) => void;
  onConfirm: () => void;
  isSubmitting: boolean;
}

const XMLWarningModal: React.FC<XMLWarningModalProps> = ({
  isOpen,
  onClose,
  isCheckboxChecked,
  setIsCheckboxChecked,
  onConfirm,
  isSubmitting,
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      isCentered
      returnFocusOnClose={false}
    >
      <ModalOverlay />
      <ModalContent maxW="520px" py={8} px={6} fontFamily="Roboto, sans-serif">
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={6} align="start">
            <Center w="full">
              <Icon viewBox="0 0 64 64" boxSize={16}>
                <path
                  d="M32 4c-1.47 0-2.82.8-3.54 2.09L3.32 56.21c-.72 1.29-.72 2.86 0 4.15C4.04 61.64 5.39 62.44 6.86 62.44h50.28c1.47 0 2.82-.8 3.54-2.09.72-1.29.72-2.86 0-4.15L35.54 6.09C34.82 4.8 33.47 4 32 4z"
                  fill="#F7B731"
                />
                <rect x="30" y="22" width="4" height="16" rx="2" fill="white" />
                <circle cx="32" cy="46" r="3" fill="white" />
              </Icon>
            </Center>
            <Text
              fontSize="xl"
              fontWeight="semibold"
              textAlign="center"
              w="full"
              color="blue.600"
            >
              Are you sure you want to continue?
            </Text>
            <Text fontSize="sm" color="gray.700">
              You’re about to overwrite a BPL with a future effective date of
              change that has already been sent for this client. Make sure these
              changes do not overwrite the future changes with ESI.
            </Text>
            <Checkbox
              isChecked={isCheckboxChecked}
              onChange={(e) => setIsCheckboxChecked(e.target.checked)}
              colorScheme="green"
            >
              I confirm the changes are accurate
            </Checkbox>
          </VStack>
        </ModalBody>
        <ModalFooter justifyContent="space-between">
          <Button
            variant="outline"
            colorScheme="green"
            onClick={onClose}
            px={6}
          >
            Go Back
          </Button>
          <Button
            colorScheme="green"
            onClick={onConfirm}
            isDisabled={!isCheckboxChecked}
            isLoading={isSubmitting}
            px={6}
          >
            Generate and Send
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default XMLWarningModal;
