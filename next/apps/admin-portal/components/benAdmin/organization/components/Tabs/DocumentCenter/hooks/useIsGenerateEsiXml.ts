import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

/**
 * Custom hook to determine if the current product is a core product
 * with carrier number 9391
 *
 * @param formMethods The form methods object from react-hook-form
 * @returns Boolean indicating if this is a core product with carrier 9391
 */
export const useIsGenerateEsiXml = (
  formMethods?: UseFormReturn<any>
): boolean => {
  const carrierNumber = formMethods?.getValues('plan.carrier_number');
  const productName = formMethods?.getValues('plan.product.name');

  const isGenerateEsiXml = useMemo(() => {
    return (
      carrierNumber === '9391' &&
      typeof productName === 'string' &&
      productName.toLowerCase() === 'esi-360'
    );
  }, [carrierNumber, productName]);

  return isGenerateEsiXml;
};
