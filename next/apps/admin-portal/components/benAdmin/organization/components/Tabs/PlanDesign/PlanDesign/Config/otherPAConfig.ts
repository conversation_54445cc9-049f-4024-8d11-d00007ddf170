import {
  generatePaths,
  getBasePath,
  replacePlaceholder,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { PLAN_DESIGN_DETAILS_BASE_PATH } from './coreConfig';

// Base path for other PA plan design details
export const PLAN_DESIGN_OTHER_PA_BASE_PATH = `${PLAN_DESIGN_DETAILS_BASE_PATH}.accumulation_other.{j}`;

// Define field names relative to the other PA base path
export const OTHER_PA_FIELDS = {
  other_accum_type_ind: 'other_accum_type_ind',
  pbc_order: 'pbc_order',
  accums_tier_name: 'accums_tier_name',
  effective_date: 'effective_date',
  expiration_date: 'expiration_date',
  notes: 'notes',
  cdh_class_code_ind: 'cdh_class_code_ind',
  carryover_phase_ind: 'carryover_phase_ind',
  describe_carryover_phase: 'describe_carryover_phase',
  benefit_period_length_ind: 'benefit_period_length_ind',
  benefit_period_length_other: 'benefit_period_length_other',
  accum_period_ind: 'accum_period_ind',
  specify_accum_period_ind: 'specify_accum_period_ind',
  priming_balances_ind: 'priming_balances_ind',
  individual_within_family_amount: 'individual_within_family_amount',
  individual_plan_amount: 'individual_within_family_amount',
  employee_1_dep_amount: 'employee_1_dep_amount',
  family_plan_amount: 'family_plan_amount',
  shared_ind: 'shared_ind',
  drug_type_status_ind: 'drug_type_status_ind',
  formulary_status_ind: 'formulary_status_ind',
  network_status_ind: 'network_status_ind',
  network_applicability_ind: 'network_applicability_ind',
  pharmacy_channel_ind: 'pharmacy_channel_ind',
  include_drug_list_ind: 'include_drug_list_ind',
  exclude_drug_list_ind: 'exclude_drug_list_ind',
  max_allowable_cap: 'max_allowable_cap',
};

/**
 * Type definition for the other PA paths.
 */
export interface OtherPAPaths extends Record<string, string> {
  other_accum_type_ind: string;
  pbc_order: string;
  accums_tier_name: string;
  effective_date: string;
  expiration_date: string;
  notes: string;
  cdh_class_code_ind: string;
  carryover_phase_ind: string;
  describe_carryover_phase: string;
  benefit_period_length_ind: string;
  benefit_period_length_other: string;
  accum_period_ind: string;
  specify_accum_period_ind: string;
  priming_balances_ind: string;
  individual_within_family_amount: string;
  individual_plan_amount: string;
  employee_1_dep_amount: string;
  family_plan_amount: string;
  shared_ind: string;
  drug_type_status_ind: string;
  formulary_status_ind: string;
  network_status_ind: string;
  network_applicability_ind: string;
  pharmacy_channel_ind: string;
  include_drug_list_ind: string;
  exclude_drug_list_ind: string;
  max_allowable_cap: string;
}

/**
 * Base other PA config with a placeholder "{i}" in each path.
 */
const otherPAConfig: OtherPAPaths = {
  other_accum_type_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.other_accum_type_ind}`,
  pbc_order: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.pbc_order}`,
  accums_tier_name: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.accums_tier_name}`,
  effective_date: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.effective_date}`,
  expiration_date: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.expiration_date}`,
  notes: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.notes}`,
  cdh_class_code_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.cdh_class_code_ind}`,
  carryover_phase_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.carryover_phase_ind}`,
  describe_carryover_phase: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.describe_carryover_phase}`,
  benefit_period_length_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.benefit_period_length_ind}`,
  benefit_period_length_other: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.benefit_period_length_other}`,
  accum_period_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.accum_period_ind}`,
  specify_accum_period_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.specify_accum_period_ind}`,
  priming_balances_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.priming_balances_ind}`,
  individual_within_family_amount: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.individual_within_family_amount}`,
  individual_plan_amount: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.individual_plan_amount}`,
  employee_1_dep_amount: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.employee_1_dep_amount}`,
  family_plan_amount: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.family_plan_amount}`,
  shared_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.shared_ind}`,
  drug_type_status_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.drug_type_status_ind}`,
  formulary_status_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.formulary_status_ind}`,
  network_status_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.network_status_ind}`,
  network_applicability_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.network_applicability_ind}`,
  pharmacy_channel_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.pharmacy_channel_ind}`,
  include_drug_list_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.include_drug_list_ind}`,
  exclude_drug_list_ind: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.exclude_drug_list_ind}`,
  max_allowable_cap: `${PLAN_DESIGN_OTHER_PA_BASE_PATH}.${OTHER_PA_FIELDS.max_allowable_cap}`,
};

/**
 * getOtherPAPath
 * Returns a single path for the given field at the specified index.
 *
 * @param field - The name of the OtherPA field
 * @param index - The index of the OtherPA in the array
 */
export function getOtherPAPath(
  field: keyof OtherPAPaths,
  index: number
): string {
  return replacePlaceholder(otherPAConfig[field], '{j}', index.toString());
}

/**
 * getOtherPAPaths
 * Returns a OtherPAPaths object where each path has its "{i}" placeholder
 * replaced by the given index.
 *
 * @param index - The index of the OtherPA in the array
 */
export function getOtherPAPaths(index: number): OtherPAPaths {
  return generatePaths(
    otherPAConfig as Record<string, string>,
    index
  ) as OtherPAPaths;
}

export function getOtherCapBasePath(
  isNewItem: boolean,
  itemIndex = -1
): string {
  return getBasePath(
    isNewItem,
    `${PLAN_DESIGN_DETAILS_BASE_PATH}[0].accumulation_other`,
    itemIndex
  );
}
