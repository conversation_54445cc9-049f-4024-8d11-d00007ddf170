import {
  defineForm<PERSON>ield,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { z } from 'zod';

import { usePicklistMaps } from '../../../../maps/picklistMaps';

export const useGenerateXmlModalForm = () => {
  const { esiSubmissionTypeMap, esiProcessModeMap } = usePicklistMaps();

  const subCategories = [
    defineSubCategory('XML Generation Setup', '', [
      defineInlineFieldGroup([
        defineFormField(
          'XML Submission Type',
          'dropdownSelect',
          'submission_type',
          '',
          {
            placeholder: 'Select type',
            optionsMap: esiSubmissionTypeMap,
            isRequired: true,
          }
        ),
        defineFormField(
          'Effective Date of Change',
          'datepicker',
          'effective_date_of_change',
          '',
          {
            placeholder: 'Select date',
            isRequired: true,
            validations: z.date({
              required_error: 'Effective date is required',
            }),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField('Case Number', 'input', 'case_number', '', {
          placeholder: 'Input text',
          isRequired: true,
          validations: z
            .string()
            .min(1, 'Case number is required')
            .max(100, 'Case number cannot exceed 100 characters'),
        }),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          '',
          'text',
          '',
          'Put N/A if you don’t have a case number'
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Select Process Mode',
          'dropdownSelect',
          'process_mode_ind',
          '',
          {
            placeholder: 'Select option',
            optionsMap: esiProcessModeMap,
            isRequired: true,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Submission Comments',
          'textarea',
          'submission_comments',
          '',
          {
            placeholder: 'Input Text',
            isRequired: false,
            validations: z.string().optional(),
            rows: 4,
            customProps: {
              resize: 'none',
              minHeight: '100px',
            },
          }
        ),
      ]),
    ]),
  ];

  return { subCategories };
};
