// ClinicalDesignServiceLayer.tsx
import { UseFormReturn } from 'react-hook-form';

import {
  extractPlanFeatures,
  generateFeatureGroupKey,
  generateFeatureTabId,
  mapPlanFeaturesToMetadata,
} from '../../../../hooks/Configurable/featureUtils';
import { usePlanDesignIndex } from '../../../../hooks/PlanDesign/usePlanDesignIndex';
import { getClinicalDesignFields } from './FieldGroups/clinicalDesignFields';

/**
 * Custom hook to generate Clinical Design field groups dynamically based on backend data
 * Uses shared utility functions from configurableFieldUtils.ts
 *
 * @param formMethods The form methods from react-hook-form
 * @returns An object containing all the field groups organized by feature
 */
export const useClinicalDesignFieldGroups = (
  formMethods: UseFormReturn<any>
) => {
  const { handleSubmit, watch } = formMethods;
  const formData = watch(); // Get the current form state

  const { planDesignIndex } = usePlanDesignIndex();

  // Return empty object if no data available
  if (!formData) {
    return { clinicalDesign: {} };
  }

  // Initialize empty object to hold our field groups
  const fieldGroups: Record<string, any> = {};

  // Extract plan features using shared utility
  const planFeatures = extractPlanFeatures(formData);

  // Return early if no plan features
  if (planFeatures.length === 0) {
    return { clinicalDesign: {} };
  }

  // Map plan features to metadata features using shared utility
  // Exclude Pharmacy Network as it belongs under Products & Services
  const relevantFeatures = mapPlanFeaturesToMetadata(
    planFeatures,
    formData.features,
    ['PharmacyNetwork']
  );

  const priorAuthReviewFeatureName =
    'UtilizationManagementPriorAuthorizationReviewer';
  const priorAuthReviewFeature = relevantFeatures.find((feature) => {
    return feature.name === priorAuthReviewFeatureName;
  });

  const remainingGroupFeatures = relevantFeatures.filter((feature) => {
    return feature.name !== priorAuthReviewFeatureName;
  });

  // Process each relevant feature to create field groups
  remainingGroupFeatures.forEach((feature: any) => {
    // Skip invalid features or those without feature_items
    // Pharmacy Network belongs under Products & Services, not here
    if (
      !feature ||
      !Array.isArray(feature.feature_items) ||
      feature.feature_items.length === 0 ||
      feature.name.includes('PharmacyNetwork')
    ) {
      return;
    }

    // Generate a unique key for this group based on the feature
    // Using the shared utility function to ensure consistency
    const groupKey = generateFeatureGroupKey(feature);

    // Get fields for this feature
    const fields = getClinicalDesignFields(
      formData,
      feature,
      planDesignIndex,
      priorAuthReviewFeature
    );

    // Only add the group if it has fields
    if (fields.length > 0) {
      // Generate tab ID using the shared utility function to ensure consistency
      const tabId = generateFeatureTabId(feature);

      // Create the field group with all necessary properties
      fieldGroups[groupKey] = {
        subtitle:
          feature.label || feature.name || `Feature ${feature.feature_id}`, // The group heading
        fields: fields, // The processed fields for this feature
        columns: 2, // Set to 2 to match form layout
        handleSubmit, // Pass through the form's submit handler
        formMethods, // Pass through the form methods
        tab: tabId, // Set the tab this group belongs to - using the shared tab ID
      };
    }
  });

  // Return the complete object with all field groups
  return {
    clinicalDesign: fieldGroups, // Wrap all groups under the clinicalDesign key
  };
};
