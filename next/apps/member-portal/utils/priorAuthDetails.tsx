export const IN_REVIEW = 'In Review';
export const INFORMATION_REQUESTED_FROM_PRESCRIBER =
  'Information Requested from Prescriber';
export const PRIOR_AUTHORIZATION_NOT_NEEDED = 'Prior Authorization Not Needed';
export const APPROVED = 'Approved';
export const APPROVED_WITH_ALTERATIONS = 'Approved with Alterations';
export const DENIED = 'Denied';
export const DUPLICATE_REQUEST = 'Duplicate Request';
export const CANCELLED_BY_PROVIDER = 'Cancelled by Provider';
export const PAUSED_WAITING_FOR_PRESCRIBER = 'Paused - Waiting for Prescriber';
export const FAULITY_NOTIFICATION = 'Faulty Notification';
export const WITHDARWN_BY_PROVIDER = 'Withdrawn by Provider';

export const getSubReason = (abortReason: string) => {
  const reasons: { [key: string]: string } = {
    Abort:
      'This request has been previously denied, it was not marked as an appeal, and no new information was provided. You or your prescriber can submit an appeal request with updated information on your behalf. ',
    'Canceled in PromptPA':
      'Your provider did not complete the prior authorization request, so this request has been cancelled. If you still need a prescription, please contact your provider to re-open the prior authorization request.',
    'PA Not Required':
      'Fill your prescription at your preferred pharmacy, covered by your health plan. Your prior authorization request has been closed, as no prior authorization is needed for the requested product at this time.',
    Dismissed:
      'Your prior authorization request has been paused and will be reopened when we receive the necessary information from your provider.  Contact your provider to resume the prior authorization process.',
    Dissmassal:
      'Your prior authorization request has been paused and will be reopened when we receive the necessary information from your provider.  Contact your provider to resume the prior authorization process.',
    Other:
      'There was a prior authorization notification sent in error. No further action is required at this time.',
    Withdrawn:
      'Your prescriber requested the prior authorization request be withdrawn.  Contact your provider to restart the prior authorization process.',
  };
  return reasons[abortReason] || '';
};

export const getNextSteps = (status: string, nextStep: string): string[] => {
  if (status === INFORMATION_REQUESTED_FROM_PRESCRIBER)
    return [
      'To proceed with your request, we require additional information from your prescriber. Our pharmacy team at RxBenefits has reached out to them for further details.',
      "Once we receive the necessary information from your prescriber, we'll promptly review your request and make a decision.",
      'Please note that obtaining and processing this additional information may extend the decision-making process by approximately 2-3 business days.',
    ];
  else if (status === IN_REVIEW)
    return [
      'A prior authorization request has been submitted. You do not need to take action. Your RxBenefits pharmacy team is reviewing this request.   ',
    ];

  switch (status) {
    case APPROVED:
      return [
        'Fill your prescription at your preferred pharmacy, covered by your health plan. ',
        'Your prior authorization request has been approved.',
      ];
    case APPROVED_WITH_ALTERATIONS:
      return [
        'Fill your prescription at your preferred pharmacy, covered by your insurance.',
        'Your prior authorization request has been partially approved for an amount that is within the limits of your health plan.',
      ];
    case DENIED:
      return [
        'Discuss prior authorization decision and potential alternatives with your prescriber.',
        'Your prior authorization request has been denied. For more detailed information view the Clinical Explanation below, or refer to the letter mailed to you.',
      ];
    case DUPLICATE_REQUEST:
      return [
        'This request has been previously denied, it was not marked as an appeal, and no new information was provided. You or your prescriber can submit an appeal request with updated information on your behalf. ',
      ];
    case CANCELLED_BY_PROVIDER:
      return [
        'Your provider did not complete the prior authorization request, so this request has been cancelled. If you still need a prescription, please contact your provider to re-open the prior authorization request.',
      ];
    case PRIOR_AUTHORIZATION_NOT_NEEDED:
      return [
        'Fill your prescription at your preferred pharmacy, covered by your health plan. Your prior authorization request has been closed, as no prior authorization is needed for the requested product at this time.',
      ];
    case PAUSED_WAITING_FOR_PRESCRIBER:
      return [
        'Your prior authorization request has been paused and will be reopened when we receive the necessary information from your provider. Contact your provider to resume the prior authorization process.',
      ];
    case FAULITY_NOTIFICATION:
      return [
        'There was a prior authorization notification sent in error.  No further action is required at this time.',
      ];
    case WITHDARWN_BY_PROVIDER:
      return [
        'Your prescriber requested the prior authorization request be withdrawn.  Contact your provider to restart the prior authorization process.',
      ];
    default:
      return [''];
  }
};

export const isStatusInArray = (status: string, arr: string[]) =>
  arr.includes(status);

export const isUpdatedWithinLast7Days = (eventEndDate: string): boolean => {
  const currentDate: Date = new Date();
  const endDate: Date = new Date(eventEndDate);

  // Ensuring both currentDate and endDate are valid Date objects
  if (isNaN(currentDate.getTime()) || isNaN(endDate.getTime())) {
    return false;
  }

  const differenceInTime: number = currentDate.getTime() - endDate.getTime();
  const differenceInDays: number = differenceInTime / (1000 * 3600 * 24);

  return differenceInDays <= 7 && differenceInDays >= 0;
};

export const getStatusBgColor = (status: string, opacity: number): string => {
  switch (status) {
    case IN_REVIEW:
    case INFORMATION_REQUESTED_FROM_PRESCRIBER:
      return opacity ? '#1C75B9' : `rgba(28, 117, 185, 0.2);`;
    case APPROVED:
    case APPROVED_WITH_ALTERATIONS:
    case PRIOR_AUTHORIZATION_NOT_NEEDED:
      return opacity ? '#00A361' : `#CCEDDF`;
    case DENIED:
      return opacity ? '#F9423A' : `#FFA3A3`;
    case DUPLICATE_REQUEST:
    case CANCELLED_BY_PROVIDER:
    case PAUSED_WAITING_FOR_PRESCRIBER:
    case FAULITY_NOTIFICATION:
    case WITHDARWN_BY_PROVIDER:
      return opacity ? '#B1B3B3' : `#C1C1C1`;
    default:
      return '';
  }
};

export const isEventUpdatedRecently = (
  eventStartDate: string,
  eventEndDate: string | null
): boolean => {
  const eventEndDateUpdatedRecently = eventEndDate
    ? isUpdatedWithinLast7Days(eventEndDate)
    : false;
  const eventStartDateUpdatedRecently =
    isUpdatedWithinLast7Days(eventStartDate);

  return (
    (eventEndDate && eventEndDateUpdatedRecently) ||
    (!eventEndDate && eventStartDateUpdatedRecently)
  );
};

export const getCursorStyle = (rowStatus: string): string => {
  return rowStatus === 'Denied' ||
    rowStatus === 'Partially Approved' ||
    rowStatus === 'Cancelled' ||
    rowStatus === 'Abort' ||
    rowStatus === 'In Progress' ||
    rowStatus === 'Approved'
    ? 'pointer'
    : 'default';
};
